<template>
  <el-dialog
    title="添加智能体"
    v-model="dialogVisible"
    :width="1200"
    @close="handleClose"
  >
    <div class="agent-selection">
      <el-button
        style="margin-bottom: 20px"
        type="primary"
        @click="createAgent"
      >
        创建智能体
      </el-button>

      <!-- 创建智能体的弹窗，用formShow控制显示隐藏 -->
      <el-dialog
        title="创建智能体"
        v-model="formShow"
        :width="600"
        @close="handleCreateAgentClose"
        :close-on-click-modal="false"
      >
        <el-form
          label-width="100px"
          :model="createAgentForm"
          class="create-agent-form"
          :rules="formRules"
          ref="createFormRef"
        >
          <el-form-item label="模型配置" prop="model">
            <el-select v-model="createAgentForm.model" placeholder="请选择模型">
              <el-option
                label="Llama-3.2-1B-Instruct"
                value="Llama-3.2-1B-Instruct"
              ></el-option>
              <el-option
                label="Llama-3.1-8B-Instruct"
                value="Llama-3.1-8B-Instruct"
              ></el-option>
              <el-option label="Qwen-7B-Chat" value="Qwen-7B-Chat"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="智能体名称" prop="name">
            <el-input
              v-model="createAgentForm.name"
              placeholder="给我取一个名称"
              :maxlength="20"
            />
          </el-form-item>
          <el-form-item label="形象照片" prop="avatar">
            <div class="upload-img">
              <el-upload
                class="avatar-uploader"
                action="#"
                :show-file-list="false"
                :on-change="handleImageChange"
                :before-upload="beforeImageUpload"
              >
                <div v-if="createAgentForm.avatar" class="uploaded-img">
                  <img :src="createAgentForm.avatar" alt="智能体形象" />
                  <el-icon><Delete @click.stop="deleteImage" /></el-icon>
                </div>
                <div v-else class="upload-btn">
                  <el-icon><Plus /></el-icon>
                  <div class="text">上传图片</div>
                </div>
              </el-upload>
              <div class="upload-tip">支持JPG、PNG格式，建议尺寸200x200px</div>
            </div>
          </el-form-item>
          <el-form-item label="智能体简介" prop="intro">
            <el-input
              v-model="createAgentForm.intro"
              type="textarea"
              placeholder="描述形象特征、风格，如卡通形象、人物、拟人化动物、性格、外形元素等"
              :maxlength="200"
              rows="3"
            />
          </el-form-item>
          <el-form-item label="开场白" prop="opening">
            <el-input
              v-model="createAgentForm.opening"
              type="textarea"
              placeholder="请输入开场白"
              :maxlength="200"
              rows="3"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="formShow = false">取消</el-button>
            <el-button
              type="primary"
              @click="confirmCreateAgent"
              :loading="isSubmitting"
            >
              确定
            </el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 使用 el-table 组件 -->
      <el-table
        ref="selectionTable"
        :data="allAgentList"
        class="agent-table"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" />
        <el-table-column label="智能体名称" width="180">
          <template #default="scope">
            <div class="agent-name-cell">
              <el-image class="elImage" :src="scope.row.avatar">
                <template #error>
                  <div class="image-slot">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image>
              <span class="agent-name">{{ scope.row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="intro" label="简介" />
      </el-table>

      <div class="pagination">
        <span>已选/{{ selectedCount }}</span>
        <el-pagination
          layout="pager, next, jumper"
          :total="total"
          :page-size="pageSize"
          :current-page="currentPage"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmSelection">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { Picture, Plus, Delete } from '@element-plus/icons-vue'
import api from '@/api/api';
// 引入API请求工具（假设你项目中有这样的工具）
// import request from '@/utils/request'
const obj = {}
location.href
  .split('?')[1]
  .split('&')
  .map((item) => {
    obj[item.split('=')[0]] = item.split('=')[1]
  })

export default {
  components: {
    Picture,
    Plus,
    Delete,
  },
  name: 'AgentSelectionModal',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    total: {
      type: Number,
      default: 24,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      formShow: false, // 控制创建智能体弹窗的显示隐藏
      isSubmitting: false, // 提交加载状态
      pageSize: 8,
      currentPage: 1,
      selectedAgents: [],
      allAgentList: [
        {
          id: '1',
          name: '智能导游A',
          avatar:
            'https://prod.shukeyun.com/maintenance/deepfile/data/2025-08-19/upload_52325c1a2757a56b1a54cfd0db658649.jpg',
          intro: '大家好，我是"云游小导"——专为景区设计的 AI 导游虚拟导游。',
        },
        {
          id: '2',
          name: '智能导游B',
          avatar:
            'https://prod.shukeyun.com/maintenance/deepfile/data/2025-08-19/upload_52325c1a2757a56b1a54cfd0db658649.jpg',
          intro: '大家好，我是"云游小导"——专为景区设计的 AI 导游虚拟导游。',
        },
        {
          id: '3',
          name: '智能导游C',
          avatar:
            'https://prod.shukeyun.com/maintenance/deepfile/data/2025-08-15/upload_1e3fcd92856e16adc98e068f742d2b8d.png',
          intro: '大家好，我是"云游小导"——专为景区设计的 AI 导游虚拟导游。',
        },
      ],
      // 创建智能体的表单数据
      createAgentForm: {
        model: '',
        name: '',
        avatar: '',
        intro: '',
        opening: '',
      },
      // 表单验证规则
      formRules: {
        model: [
          { required: true, message: '请选择模型配置', trigger: 'change' },
        ],
        name: [
          { required: true, message: '请输入智能体名称', trigger: 'blur' },
          {
            min: 2,
            max: 20,
            message: '名称长度在 2 到 20 个字符',
            trigger: 'blur',
          },
        ],
        avatar: [
          { required: true, message: '请上传形象照片', trigger: 'change' },
        ],
        intro: [
          { required: true, message: '请输入智能体简介', trigger: 'blur' },
          {
            min: 5,
            max: 200,
            message: '简介长度在 5 到 200 个字符',
            trigger: 'blur',
          },
        ],
        opening: [
          { required: true, message: '请输入开场白', trigger: 'blur' },
          {
            min: 5,
            max: 200,
            message: '开场白长度在 5 到 200 个字符',
            trigger: 'blur',
          },
        ],
      },
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      this.resetSelection()
    },
  },
  computed: {
    selectedCount() {
      return this.selectedAgents.length
    },
  },
  methods: {
    async getList() {
      const res = await api.getAgentList({
        pageSize: this.pageSize,
        current: this.currentPage,
        storeId: obj.storeId,
      })
    },
    handleSelectionChange(selection) {
      this.selectedAgents = selection
    },
    handleClose() {
      this.$emit('update:visible', false)
      this.resetSelection()
    },
    resetSelection() {
      this.selectedAgents = []
      if (this.$refs.selectionTable) {
        this.$refs.selectionTable.clearSelection()
      }
    },
    confirmSelection() {
      this.$emit('confirm', [...this.selectedAgents])
      this.dialogVisible = false
      this.resetSelection()
    },
    createAgent() {
      this.formShow = true
      // 重置表单
      this.$nextTick(() => {
        this.$refs.createFormRef.resetFields()
      })
    },
    // 处理创建智能体弹窗关闭事件
    handleCreateAgentClose() {
      this.formShow = false
      // 重置表单
      this.$refs.createFormRef.resetFields()
    },
    // 图片上传前验证
    beforeImageUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        this.$message.error('上传图片只能是 JPG/PNG 格式!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      return true
    },
    // 图片上传change事件
    handleImageChange(file) {
      // 这里只是预览，实际项目中需要先上传到服务器
      this.createAgentForm.avatar = URL.createObjectURL(file.raw)
    },
    // 删除已上传图片
    deleteImage() {
      this.createAgentForm.avatar = ''
    },
    // 确认创建智能体（预留接口调用位置）
    async confirmCreateAgent() {
      // 1. 表单验证
      try {
        await this.$refs.createFormRef.validate()
      } catch (error) {
        return this.$message.warning('请完善表单信息后再提交')
      }

      // 2. 准备提交数据
      this.isSubmitting = true

      try {
        // 3. 接口调用（预留的接口调用位置）
        // 注意：这里需要根据实际接口要求调整参数格式
        const formData = new FormData()
        formData.append('model', this.createAgentForm.model)
        formData.append('name', this.createAgentForm.name)
        formData.append('intro', this.createAgentForm.intro)
        formData.append('opening', this.createAgentForm.opening)

        // 如果是本地预览地址，需要替换为实际文件对象
        if (this.createAgentForm.avatar.startsWith('blob:')) {
          // 这里需要找到对应的文件对象，实际项目中可能需要在handleImageChange时保存
          // formData.append('avatar', fileObject)
        } else {
          formData.append('avatar', this.createAgentForm.avatar)
        }

        // 4. 调用API（实际项目中取消注释）
        /* const response = await request({
          url: '/api/agents/create',  // 替换为实际接口地址
          method: 'post',
          data: formData,
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }) */

        // 模拟接口成功响应
        const response = {
          code: 200,
          data: {
            id: Date.now().toString(), // 模拟ID
            ...this.createAgentForm,
          },
        }

        // 5. 处理成功响应
        if (response.code === 200) {
          this.$message.success('智能体创建成功')

          // 6. 将新创建的智能体添加到列表中
          this.allAgentList.unshift(response.data)

          // 7. 关闭弹窗
          this.formShow = false
        } else {
          this.$message.error(response.message || '创建智能体失败')
        }
      } catch (error) {
        console.error('创建智能体接口调用失败:', error)
        this.$message.error('网络异常，请稍后重试')
      } finally {
        // 8. 无论成功失败，都关闭加载状态
        this.isSubmitting = false
      }
    },
    handleSizeChange(size) {
      this.pageSize = size
    },
    handleCurrentChange(page) {
      this.currentPage = page
    },
  },
  mounted() {
    this.getList()
  },
}
</script>

<style lang="less" scoped>
.agent-selection {
  padding: 20px;

  .agent-table {
    width: 100%;
    margin-bottom: 15px;

    :deep(.el-table__header-wrapper) {
      th {
        background-color: #f5f7fa;
        font-weight: bold;
      }
    }

    :deep(.el-table__row) {
      &:hover {
        background-color: #f9f9f9;
      }
    }

    .agent-name-cell {
      display: flex;
      align-items: flex-start;
      padding: 8px 0;
      .elImage {
        width: 60px;
        height: 60px;
        border-radius: 4px;
        margin-right: 6px;
      }

      .agent-name {
        font-weight: 700;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
  }

  .dialog-footer {
    text-align: right;
  }

  // 创建智能体弹窗表单样式
  .create-agent-form {
    .el-form-item {
      margin-bottom: 20px;
    }
    .upload-img {
      .avatar-uploader {
        .uploaded-img {
          position: relative;
          width: 120px;
          height: 120px;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 4px;
          }
          .el-icon-delete {
            position: absolute;
            right: -6px;
            top: -6px;
            background: #fff;
            border-radius: 50%;
            padding: 2px;
            cursor: pointer;
            box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
          }
        }
        .upload-btn {
          width: 120px;
          height: 120px;
          border: 1px dashed #d9d9d9;
          border-radius: 4px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          color: #999;
          cursor: pointer;
          .text {
            margin-top: 8px;
          }
          &:hover {
            border-color: #409eff;
            color: #409eff;
          }
        }
      }
      .upload-tip {
        margin-top: 8px;
        font-size: 12px;
        color: #999;
      }
    }
  }
}
</style>
