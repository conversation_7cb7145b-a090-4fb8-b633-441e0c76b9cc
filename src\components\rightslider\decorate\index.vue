<template>
  <div class="decorate">
    <!-- 表单 -->
    <el-form label-width="80px" :model="datas" :rules="rules">
      <!-- 页面名称 -->
      <el-form-item label="页面名称" :hide-required-asterisk="true" prop="name">
        <el-input
          v-model="datas.name"
          placeholder="请输入"
          maxlength="25"
          show-word-limit
        />
      </el-form-item>
      <!-- 页面描述 -->
      <el-form-item
        label="页面描述"
        :hide-required-asterisk="true"
        prop="details"
      >
        <el-input v-model="datas.details" placeholder="请输入" />
      </el-form-item>
      <!-- 背景颜色 -->
      <el-form-item label="背景颜色">
        <el-color-picker
          v-model="datas.bgColor"
          show-alpha
          :predefine="predefineColors"
          @active-change="(e) => (datas.bgColor = e)"
        >
        </el-color-picker>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'decorate',
  props: {
    datas: Object,
  },
  data() {
    return {
      rules: {
        // 校验表单输入
        name: [
          // 页面名称
          { required: true, message: '请输入页面名称', trigger: 'blur' },
        ],
        details: [
          // 页面描述
          { required: true, message: '请输入页面描述', trigger: 'blur' },
        ],
      },
      pickeShow: false, // 颜色选择器是否显示
      predefineColors: [
        // 颜色选择器预设
        'rgba(249, 249, 249, 10)',
      ],
    }
  },
  setup() {
    return {}
  },
  methods: {},
}
</script>

<style scoped lang="less">
/* 页面设置 */
.decorate {
}
</style>
