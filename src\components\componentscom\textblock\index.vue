<template>
  <div
    :style="{
      width: datas.width + 'mm',
      // height: datas.height + 'px',
    }"
    class="textblock"
  >
    <div
      class="textblock-item"
      v-for="(item, index) in tableList"
      :key="index"
      :style="{
        width: (1 / datas.columnNum) * 100 + '%',
        textAlign: datas.alignType,
        lineHeight: datas.lineSpacing + 'mm',
        fontSize: item.fontSize + 'mm',
      }"
    >
      <span v-if="item.showTitle">{{ item.title }}：</span>{{ item.value }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'textblock',
  props: {
    datas: Object,
    tableData: Array,
  },
  data() {
    return {
      swiperIndex: 0,
      timeId: null,
      defaultImg: require('@/assets/images/slideshow.svg'),
    }
  },
  computed: {
    tableList() {
      return this.datas.tableData.filter((e) => e.show)
    },
  },
  methods: {},
  created() {},
  watch: {},
}
</script>

<style scoped lang="less">
.textblock {
  display: flex;
  flex-wrap: wrap;
  word-break: break-all;
  .textblock-item {
    // padding: 10px;
  }
}
</style>
