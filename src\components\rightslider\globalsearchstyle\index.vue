<template>
  <div class="pictureadsstyle">
    <!-- 表单 -->
    <el-form label-width="80px" label-position="left" :model="datas">
      <!-- 添加热词 -->

      <titlecom
        title="添加热词"
        tips="拖动选择热词可以对其排序，最多可以添加 10 个"
      />
      <el-form-item label="添加热词">
        <el-radio-group v-model="datas.hotWordType" class="radi1">
          <el-radio label="auto">自动</el-radio>
          <el-radio label="manual">手动</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- 添加热词 -->
      <div>
        <vuedraggable
          :list="datas.hotWordList"
          item-key="index"
          :forceFallback="true"
          :animation="200"
        >
          <template #item="{ element }">
            <section class="imgList">
              <!-- <van-icon class="imgClose" name="close" @click="delWord(index)" /> -->
              <el-input
                placeholder="请输入"
                v-model="element.text"
                :maxlength="10"
              ></el-input>
              <svgcom name="close" class="close-icon" @click="delWord(index)" />
            </section>
          </template>
        </vuedraggable>
      </div>
      <!-- 添加图片 -->
      <el-button
        class="imgAdd"
        :disabled="
          datas.hotWordType == 'auto'
            ? datas.hotWordList.length > 4
            : datas.hotWordList.length > 9
        "
        @click="addWord"
      >
        添加热词（{{ datas.hotWordList.length }}/{{
          datas.hotWordType == 'auto' ? '5' : '10'
        }}）
      </el-button>
      <!-- 下划线 -->
      <div class="bor"></div>
      <titlecom title="组件样式" />
      <!-- 显示位置 -->
      <el-form-item label="显示位置">
        <el-select
          style="width: 100%"
          v-model="datas.positionType"
          placeholder="请选择"
        >
          <el-option
            v-for="(value, key) in positionTypeList"
            :key="key"
            :value="key"
            :label="value"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- 框体样式 -->
      <el-form-item label="框体样式">
        <el-select
          style="width: 100%"
          v-model="datas.borderType"
          placeholder="请选择"
        >
          <el-option
            v-for="(value, key) in borderTypeList"
            :key="key"
            :value="key"
            :label="value"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- 文本位置 -->
      <el-form-item label="文本位置">
        <el-select
          style="width: 100%"
          v-model="datas.textAlign"
          placeholder="请选择"
        >
          <el-option
            v-for="(value, key) in textAlignList"
            :key="key"
            :value="key"
            :label="value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="框体高度">
        <el-slider
          v-model="datas.searchHeight"
          :max="100"
          :min="20"
          input-size="small"
          show-input
        >
        </el-slider>
      </el-form-item>
      <!-- 背景颜色 -->
      <el-form-item label="背景颜色">
        <el-color-picker
          v-model="datas.bgColor"
          show-alpha
          :predefine="predefineColors"
        >
        </el-color-picker>
      </el-form-item>
      <!-- 框体颜色 -->
      <el-form-item label="框体颜色">
        <el-color-picker
          v-model="datas.borderColor"
          show-alpha
          :predefine="predefineColors"
        >
        </el-color-picker>
      </el-form-item>
      <!-- 文本颜色 -->
      <el-form-item label="文本颜色">
        <el-color-picker
          v-model="datas.textColor"
          show-alpha
          :predefine="predefineColors"
        >
        </el-color-picker>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import vuedraggable from 'vuedraggable' //拖拽组件

export default {
  name: 'globalsearchstyle',
  components: { vuedraggable },
  props: {
    datas: Object,
  },
  data() {
    return {
      positionTypeList: { initial: '正常模式', sticky: '滚动到顶部固定' },
      borderTypeList: { quadrate: '方形', round: '圆形' },
      textAlignList: { left: '居左', center: '居中' },
      predefineColors: [
        // 颜色选择器预设
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585',
        '#409EFF',
        '#909399',
        '#C0C4CC',
        'rgba(255, 69, 0, 0.68)',
        'rgb(255, 120, 0)',
        'hsv(51, 100, 98)',
        'hsva(120, 40, 94, 0.5)',
        'hsl(181, 100%, 37%)',
        'hsla(209, 100%, 56%, 0.73)',
        '#c7158577',
      ],
      // // 轮播类型
      // list: [
      //   {
      //     name: '一行一个',
      //     value: '0',
      //     icon: 'swiper_type_0',
      //   },
      //   {
      //     name: '轮播海报',
      //     value: '1',
      //     icon: 'swiper_type_1',
      //   },
      //   {
      //     name: '双层轮播',
      //     value: '2',
      //     icon: 'swiper_type_2',
      //   },
      //   {
      //     name: '横向滑动',
      //     value: '3',
      //     icon: 'swiper_type_3',
      //   },
      // ],
      // // 跳转类型
      // optionsType: [
      //   {
      //     type: '0',
      //     name: '内链',
      //   },
      //   {
      //     type: '1',
      //     name: '外链',
      //   },
      // ],
      // // 跳转页面
      // pagePaths: [
      //   {
      //     name: '门票预订',
      //     value: 'ticketList',
      //   },
      //   {
      //     name: '权益卡',
      //     value: 'travelCardList',
      //   },
      //   {
      //     name: '活动资讯',
      //     value: 'information',
      //   },
      //   {
      //     name: '攻略游记',
      //     value: 'travelNotes',
      //   },
      // ],
      // // 指示器类型
      // indicators: { 0: '样式一', 1: '样式二', 2: '样式三', 3: '样式四' },
      // screenNums: { 0: '1 个图片', 1: '2 个图片' },
      // emptyText: '',
    }
  },
  methods: {
    // 添加热词
    addWord() {
      this.datas.hotWordList.push({
        text: '',
      })
    },
    // 删除热词
    delWord(index) {
      this.datas.hotWordList.splice(index, 1)
    },
  },
  watch: {
    'datas.hotWordType': {
      handler(v) {
        this.datas.hotWordList = []
      },
      deep: true, // 深度监听嵌套属性的变化
    },
  },
}
</script>

<style scoped lang="less">
.pictureadsstyle {
  .imgList {
    padding: 10px;
    margin: 15px 0;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 0 4px 0 rgba(10, 42, 97, 0.2);
    display: flex;
    align-items: center;
    position: relative;
    gap: 10px;
    .close-icon {
      width: 20px;
      height: 20px;
    }
    .imgClose {
      position: absolute;
      right: 10px;
      top: 10px;
      cursor: pointer;
      font-size: 19px;
    }
    .imgText {
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      justify-content: space-between;
      .select-type {
        display: flex;
        > div:first-child {
          width: 72px;
          :deep(input) {
            border-right-width: 0;
            border-radius: 4px 0 0 4px;
          }
        }
        > div:last-child {
          flex: 1;
          :deep(input) {
            border-radius: 0 4px 4px 0;
          }
        }
      }
    }
  }
  .imgAdd {
    width: 100%;
    height: 40px;
    border-style: dashed;
  }
}
</style>
