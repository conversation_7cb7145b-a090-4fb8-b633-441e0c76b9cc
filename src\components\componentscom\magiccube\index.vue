<template>
  <div class="magiccube">
    <img
      draggable="false"
      v-show="!showimageList"
      src="../../../assets/images/mor.png"
      alt=""
      style="width: 100%"
    />
    <div v-show="showimageList" style="overflow: hidden">
      <div
        class="rubikes-cube"
        :style="{
          width: `${375}px`,
          height: `${realHeight}px`,
          // margin: `-${datas.imgMargin}px`,
        }"
      >
        <div
          v-for="(item, index) in magiccubeList"
          :key="index"
          class="rubikes-cube-item"
          :style="{
            top: `${item.top}px`,
            left: `${item.left}px`,
            width: `${item.width}px`,
            height: `${item.height}px`,
            paddingTop: `${item.paddingTop}px`,
            paddingLeft: `${item.paddingLeft}px`,
            paddingRight: `${item.paddingRight}px`,
            paddingBottom: `${item.paddingBottom}px`,
          }"
        >
          <div
            :style="{
              width: '100%',
              height: '100%',
              backgroundImage: `url(${item.src})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              borderRadius: `${datas.borderRadius}px`,
            }"
          ></div>
        </div>
      </div>
    </div>

    <!-- 删除组件 -->
    <slot name="deles" />
  </div>
</template>

<script>

export default {
  name: 'magiccube',
  props: {
    datas: {
      type: Object,
      default: () => ({
        rubiksStyle: {
          height: 375,
          number: 4,
          list: [],
        },
        pageMargin: 0,
        imgMargin: 0,
      }),
    },
  },
  data: () => ({
    magiccubeList: [],
    realHeight: 375,
  }),
  computed: {
    showimageList() {
      if (this.datas?.rubiksStyle && this.datas?.rubiksStyle.list) {
        return this.datas?.rubiksStyle?.list.some((item) => item.src !== '')
      } else {
        return false
      }
    },
  },
  watch: {
    showimageList(val) {
      if (val) {
        this.setImg()
      }
    },
    'datas.pageMargin': {
      handler(val) {
        if (
          this.datas.rubiksStyle &&
          this.datas.rubiksStyle.list &&
          this.datas.rubiksStyle.list.length > 0
        ) {
          const boxWidth = 375
          this.magiccubeList = this.datas.rubiksStyle.list.map((item) => {
            // 页面间隔
            if (item.left === 0) {
              item.paddingLeft = val
            }
            if (item.left + item.width === boxWidth) {
              item.paddingRight = val
            }

            return {
              ...item,
            }
          })
        }
      },
      immediate: true,
    },
    'datas.imgMargin'(val) {
      if (
        this.datas.rubiksStyle &&
        this.datas.rubiksStyle.list &&
        this.datas.rubiksStyle.list.length > 0
      ) {
        const boxHeight = this.datas.rubiksStyle.height
        const boxWidth = 375

        this.magiccubeList = this.datas.rubiksStyle.list.map((item) => {
          // 每个图片需要减去的宽高
          // const decWidth = (val * item.width) / boxWidth
          // const decHeight = (val * item.height) / boxHeight
          // // 前面图片需要减去的宽度
          // const decWidthLeft = (val * item.left) / boxWidth
          // // 前面图片需要减去的高度
          // const decHeightTop = (val * item.top) / boxHeight
          // // console.log('每个图片需要减去的宽高：', decWidth, decHeight)
          // console.log('leftttt:' + item.left, val)

          // 图片间距
          if (item.top === 0) {
            item.paddingTop = 0
          } else {
            item.paddingTop = val / 2
          }
          if (item.left === 0) {
            item.paddingLeft = 0
          } else {
            item.paddingLeft = val / 2
          }
          if (item.left + item.width === boxWidth) {
            item.paddingRight = 0
          } else {
            item.paddingRight = val / 2
          }
          if (item.top + item.height === this.realHeight) {
            item.paddingBottom = 0
          } else {
            item.paddingBottom = val / 2
          }

          // 页面间隔
          if (item.left === 0) {
            item.paddingLeft = item.pageMargin
          }
          if (item.left + item.width === boxWidth) {
            item.paddingRight = item.pageMargin
          }

          console.log('imgMarginnnnn', item)

          return {
            ...item,
            top: item.top,
            left: item.left,
            // top: item.top + (val - decHeightTop),
            // left: item.left + (val - decWidthLeft),
            // width: item.width - decWidth * 2,
            // height: item.height - decHeight * 2,
            width: item.width,
            height: item.height,
            margin: val / 2,
          }
        })
      }
    },
    'datas.rubiksStyle.list': {
      handler(val) {
        if (val && val.length > 0) {
          const realHeight = val.reduce((max, item) => {
            return Math.max(max, item.top + item.height);
          }, 0);
          this.realHeight = realHeight
        }
      },
      immediate: true,
      deep: true,
    },
  },
  created() {
    this.setImg()
  },
  methods: {
    setImg() {
      this.magiccubeList = this.datas.rubiksStyle
        ? this.datas.rubiksStyle.list
        : []
    },
  },
}
</script>

<style scoped lang="less">
.magiccube {
  position: relative;
  /* 布局 */
  .imgone {
    &:last-of-type {
      img {
        padding-bottom: 0 !important;
        padding-right: 0 !important;
      }
    }
    &:first-of-type {
      img {
        padding-top: 0 !important;
        padding-right: 0 !important;
      }
    }
  }
  .imgtow {
    &:first-of-type {
      img {
        padding-bottom: 0 !important;
        padding-left: 0 !important;
      }
    }
    &:last-of-type {
      img {
        padding-bottom: 0 !important;
        padding-right: 0 !important;
      }
    }
  }
  .hw {
    width: 100%;
    position: relative;
    img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }
  .buju {
    &.buju0 {
      display: flex;
      justify-content: space-around;
      flex-wrap: wrap;
    }
    &.buju4 {
      display: flex;
      width: 100%;
      height: 300px;
      flex-direction: row;
      justify-content: space-around;
    }
    .active {
      background: #e0edff;
      border: 1px solid #1890ff;
      color: #1890ff;
      z-index: 2;
    }
    .rubiksCubeType {
      display: inline-flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      &.rubiksCubeType0 {
        width: 50%;
        // height: 200px;
        &:first-of-type {
          img {
            padding-left: 0 !important;
            padding-top: 0 !important;
            padding-bottom: 0 !important;
          }
        }
        &:last-of-type {
          img {
            padding-right: 0 !important;
            padding-top: 0 !important;
            padding-bottom: 0 !important;
          }
        }
        img {
          width: 100%;
          // height: 200px;
          display: block;
        }
      }
      &.rubiksCubeType1 {
        width: 33.333%;
        &:nth-of-type(1) {
          margin-left: 0 !important;
          margin-top: 0 !important;
          margin-bottom: 0 !important;
        }
        &:nth-of-type(2) {
          margin-top: 0 !important;
          margin-left: 0 !important;
          margin-bottom: 0 !important;
        }
        &:nth-of-type(3) {
          margin-top: 0 !important;
          margin-right: 0 !important;
          margin-bottom: 0 !important;
        }
        img {
          width: 100%;
          height: 150px;
          display: block;
        }
      }
      &.rubiksCubeType2 {
        width: 25%;
        &:nth-of-type(1) {
          margin-left: 0 !important;
          margin-top: 0 !important;
          margin-bottom: 0 !important;
        }
        &:nth-of-type(2) {
          margin-top: 0 !important;
          margin-left: 0 !important;
          margin-bottom: 0 !important;
        }
        &:nth-of-type(3) {
          margin-top: 0 !important;
          margin-left: 0 !important;
          margin-bottom: 0 !important;
        }
        &:nth-of-type(4) {
          margin-top: 0 !important;
          margin-right: 0 !important;
          margin-bottom: 0 !important;
        }
        img {
          width: 100%;
          height: 150px;
          display: block;
        }
      }
      &.rubiksCubeType3 {
        width: 50%;
        padding-top: 50%;
        position: relative;
        &:nth-of-type(1) {
          img {
            padding-top: 0 !important;
            padding-left: 0 !important;
          }
        }
        &:nth-of-type(2) {
          img {
            padding-top: 0 !important;
            padding-right: 0 !important;
          }
        }
        &:nth-of-type(3) {
          img {
            padding-bottom: 0 !important;
            padding-left: 0 !important;
          }
        }
        &:nth-of-type(4) {
          img {
            padding-bottom: 0 !important;
            padding-right: 0 !important;
          }
        }
        img {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
        }
      }
      &.rubiksCubeType4 {
        width: 187px;
        height: 187px;
        img {
          width: 100%;
          height: 100%;
          display: block;
        }
      }
    }
  }
}

.rubikes-cube {
  position: relative;
  .rubikes-cube-item {
    font-size: 14px;
    position: absolute;
    color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    background-size: cover;
  }
}
</style>
