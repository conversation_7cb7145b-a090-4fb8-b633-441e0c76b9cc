<template>
  <div class="componenManagement">
    <p class="Prompt">点击编辑，拖拽排序</p>
    <vuedraggable
      :list="data"
      item-key="index"
      :forceFallback="true"
      :animation="200"
      filter=".delDragitem"
      @end="handleDragEnd"
    >
      <template #item="{ element, index }">
        <div
          :class="[
            'item',
            element.text == '底部导航' ? 'delDragitem' : '',
            internalSelectedIndex === index ? 'selected' : '',
          ]"
          @click="handleClick(element, index)"
        >
          <p>{{ element.text }}</p>
          <van-icon
            class="delete-icon"
            name="delete-o"
            style="cursor: pointer"
            @click.stop="onConfirms(index)"
          />
        </div>
      </template>
    </vuedraggable>
  </div>
</template>

<script>
import vuedraggable from 'vuedraggable'

export default {
  name: 'componenmanagement',
  props: {
    datas: Array,
    currentSelectedIndex: Number, // 接收父组件传递的选中索引
  },
  components: { vuedraggable },
  data() {
    return {
      data: this.datas,
      internalSelectedIndex: -1, // 改为不同的名称避免冲突
    }
  },
  methods: {
    handleClick(element, index) {
      this.internalSelectedIndex = index
      this.$emit('activeComponent', element, index)
    },
    onConfirms(index) {
      if (index === this.internalSelectedIndex) {
        this.internalSelectedIndex = -1
      } else if (index < this.internalSelectedIndex) {
        this.internalSelectedIndex--
      }

      this.data.splice(index, 1)
      this.$emit('componenmanagement', this.data)
    },
    handleDragEnd(evt) {
      const { oldIndex, newIndex } = evt

      // 拖拽未选中项时不改变选中状态
      if (this.internalSelectedIndex !== oldIndex) {
        this.$emit('update-selected-index', this.internalSelectedIndex)
        return
      }

      // 处理选中项拖拽的情况
      this.internalSelectedIndex = newIndex
      this.$emit('update-selected-index', newIndex)
    },
    onDragEnd(evt) {
      const { oldIndex, newIndex } = evt
      if (this.selectedIndex === oldIndex) {
        this.selectedIndex = newIndex
      } else if (
        oldIndex < this.selectedIndex &&
        newIndex > this.selectedIndex
      ) {
        this.selectedIndex--
      } else if (
        oldIndex > this.selectedIndex &&
        newIndex < this.selectedIndex
      ) {
        this.selectedIndex++
      }
    },
  },
  watch: {
    currentSelectedIndex(newVal) {
      this.internalSelectedIndex = newVal
    },
    datas(newVal) {
      this.data = newVal
    },
    data(newVal) {
      this.$emit('componenmanagement', newVal)
    },
  },
}
</script>

<style scoped lang="less">
.componenManagement {
  .item {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    font-size: 14px;
    cursor: all-scroll;
    color: #323233;
    border-radius: 2px;
    background-color: #fff;
    box-shadow: 0 0 4px 0 rgba(10, 42, 97, 0.2);
    margin-bottom: 10px;
    transition: all 0.2s ease;

    i {
      color: #999;
    }
    .delete-icon {
      display: none;
    }

    &:hover {
      ::v-deep .delete-icon {
        display: inline-block;
      }
    }
  }

  .item.selected {
    background-color: #e6f7ff;
    border-left: 3px solid #1890ff;
    color: #1890ff;
  }

  .item.delDragitem.selected {
    background-color: rgba(22, 119, 255, 0.2);
  }

  .delDragitem {
    background-color: rgba(10, 42, 97, 0.2);
    cursor: no-drop;
    &:hover {
      ::v-deep .delete-icon {
        display: inline-block;
      }
    }
  }
}
</style>
