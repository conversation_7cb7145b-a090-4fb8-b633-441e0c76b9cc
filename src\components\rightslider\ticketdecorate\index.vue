<template>
  <div class="decorate">
    <!-- 表单 -->
    <el-form label-width="80px" :model="datas" :rules="rules">
      <!-- 页面名称 -->
      <el-form-item label="模板名称" :hide-required-asterisk="true" prop="name">
        <el-input
          v-model="datas.name"
          placeholder="请输入"
          maxlength="10"
          show-word-limit
        />
      </el-form-item>
      <!-- 门票高度 -->
      <el-form-item label="门票高度">
        <el-slider
          v-model="datas.height"
          :min="10"
          :max="300"
          show-input
        ></el-slider>
      </el-form-item>
      <!-- 门票宽度 -->
      <el-form-item label="门票宽度">
        <el-slider
          v-model="datas.width"
          :min="10"
          :max="300"
          show-input
        ></el-slider>
      </el-form-item>
      <titlecom title="背景图片" tips="背景仅用于预览，非实际打印" />
      <!-- 背景图片 -->
      <el-form-item label="上传图片">
        <upload v-model="datas.bgUrl" />
      </el-form-item>
      <!-- 背景填充方式 -->
      <el-form-item label="填充方式">
        <el-radio-group v-model="datas.fillType" class="radi1">
          <el-radio label="full">拉伸</el-radio>
          <el-radio label="contain">比例不变</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import upload from '../../upload' //图片上传
export default {
  name: 'ticketdecorate',
  components: { upload },
  props: {
    datas: Object,
  },
  data() {
    return {
      tableData: [
        {
          date: '2016-05-02',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1518 弄',
        },
        {
          date: '2016-05-04',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1517 弄',
        },
        {
          date: '2016-05-01',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1519 弄',
        },
        {
          date: '2016-05-03',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1516 弄',
        },
      ],
      rules: {
        // 校验表单输入
        name: [
          // 页面名称
          { required: true, message: '请输入页面名称', trigger: 'blur' },
        ],
        details: [
          // 页面描述
          { required: true, message: '请输入页面描述', trigger: 'blur' },
        ],
      },
      pickeShow: false, // 颜色选择器是否显示
      predefineColors: [
        // 颜色选择器预设
        'rgba(249, 249, 249, 10)',
      ],
    }
  },
  setup() {
    return {}
  },
  methods: {},
}
</script>

<style scoped lang="less">
/* 页面设置 */
.decorate {
}
</style>
