<template>
  <div class="layout">
    <section class="subject">
      <!-- 子路由 -->
      <router-view v-slot="{ Component }">
        <component :is="Component" />
      </router-view>
    </section>
  </div>
</template>

<script>
export default {
  name: 'layout',
}
</script>

<style lang="less" scoped>
.layout {
  width: 100%;
  height: 100%;
  /* 主体 */
  .subject {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
  }
}
</style>
