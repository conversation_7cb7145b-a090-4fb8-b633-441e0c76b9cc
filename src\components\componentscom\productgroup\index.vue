<template>
  <div
    class="productgroup"
    :style="{
      paddingLeft: conf.pageMargin,
      paddingRight: conf.pageMargin,
    }"
  >
    <div class="group-box">
      <div class="group-title">
        <div
          class="group-title-item"
          v-for="(item, index) in datas.groupList"
          :key="index"
        >
          <div v-if="!index" class="line"></div>
          {{ item.text }}
        </div>
      </div>
      <div class="group-list" :style="{}">
        <div
          class="product"
          :style="{
            borderRadius: conf.borderRadius,
            marginLeft: index !== 0 ? conf.productMargin : 0,
            // flex: 1,
            width:
              conf.groupType === '1'
                ? '100%'
                : [137, 120, 120, 137][datas.scaleType] + 'px',
            aspectRatio: conf.scaleType,
            flex: conf.groupType === '1' ? 1 : 'none',
          }"
          v-for="(item, index) in ticketList.slice(0, 3 - conf.groupType)"
          :key="index"
        >
          <img
            class="product-bg"
            :src="item.picUrl"
            alt=""
            :style="{
              objectFit: conf.aspectType === 'fill' ? 'cover' : 'contain',
            }"
          />
          <div class="product-price" v-if="conf.priceSwitch">
            ¥{{ item.salePrice }}起
          </div>
          <div
            v-if="conf.textSwitch"
            class="product-title"
            :style="{
              fontWeight: conf.textType,
              textAlign: conf.pointType,
            }"
          >
            {{ item.goodsName }}
          </div>
        </div>
      </div>
    </div>

    <!-- 删除组件 -->
    <slot name="deles" />
  </div>
</template>
<script>
import api from '@/api/api';
// URL 传参
const obj = {}
location.href
  .split('?')[1]
  .split('&')
  .map((item) => {
    obj[item.split('=')[0]] = item.split('=')[1]
  })
export default {
  name: 'productgroup',
  props: {
    datas: Object,
  },
  data() {
    return {}
  },
  created() {
    const storeGoodsIds = []
    this.datas.groupList?.forEach((item) => {
      storeGoodsIds.push(...item.goodsList)
    })
    api
      .getDesignerGoodsList({
        storeId: obj.storeId,
        storeGoodsIds,
      })
      .then(({ data }) => {
        data.forEach((item) => {
          this.$store.state.goodsObj[item.storeGoodsId] = item
        })
      })
  },
  computed: {
    ticketList() {
      let goodsList = this.datas.groupList?.[0]?.goodsList || []
      let list = []
      if (goodsList.length) {
        list = goodsList.map((item) => {
          let obj = this.$store.state.goodsObj[item]
          return {
            ...obj,
            picUrl: obj?.picUrl ? process.env.VUE_APP_IMG_HOST + obj.picUrl.split(',')[0] : require('@/assets/images/pic.svg'),
          }
        })
      } else {
        list = new Array(3).fill({
          goodsName: '商品名称',
          picUrl: require('@/assets/images/default_icon.svg'),
          sellingPrice: 100,
        })
      }
      return list
    },
    conf() {
      const style = {}
      console.log('this.datas')
      const config = this.datas
      console.log(config.textType)
      // 文字样式
      if (config.textType === 'bold') {
        style.textType = 'bold'
      } else {
        style.textType = 'normal'
      }
      // 图片比例
      if (config.scaleType === '0') {
        style.scaleType = '3/2'
      } else if (config.scaleType === '1') {
        style.scaleType = '1/1'
      } else if (config.scaleType === '2') {
        style.scaleType = '3/4'
      } else if (config.scaleType === '3') {
        style.scaleType = '16/9'
      }

      // 图片圆角
      if (config.borderRadius) style.borderRadius = config.borderRadius + 'px'
      // 商品间距
      if (config.productMargin)
        style.productMargin = config.productMargin + 'px'
      // 页面边距
      if (config.pageMargin) style.pageMargin = config.pageMargin + 'px'
      return {
        ...config,
        ...style,
      }
    },
  },
}
</script>

<style scoped lang="less">
.productgroup {
  position: relative;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  .group-box {
    padding-bottom: 15px;
    border-radius: 6px;
    overflow: hidden;
    background: linear-gradient(180deg, #bbd9ff 0%, #f6fcff 36%, #eef9ff 100%);
    .group-title {
      display: flex;
      justify-content: space-around;
      color: #14131f;
      font-size: 14px;
      .group-title-item {
        text-align: center;
        margin: 12px 0 15px;
        flex: 1;
        position: relative;
        &:not(:last-child) {
          border-right: 2px solid rgba(151, 151, 151, 0.24);
        }
        .line {
          width: 17px;
          height: 3px;
          background: #349fff;
          border-radius: 5px;
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          top: -6px;
        }
      }
      .group-title-item:first-child {
        color: #349fff;
      }
    }
    .group-list {
      display: flex;
      padding: 0 10px;
      .product {
        flex: none;
        overflow: hidden;
        width: 120px;
        position: relative;
        .product-bg {
          width: 100%;
          height: 100%;
          object-fit: cover;
          background: #e7e7e7;
        }
        .product-price {
          position: absolute;
          top: 0;
          right: 0;
          background: linear-gradient(180deg, #efe6c9 0%, #ffcb85 100%);
          border-radius: 0px 0px 0px 12px;
          color: #f43636;
          font-size: 11x;
          font-weight: 600;
          line-height: 16px;
          padding: 3px;
        }
        .product-title {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          font-size: 13px;
          color: #ffffff;
          padding: 5px;
          background: linear-gradient(
            180deg,
            rgba(0, 0, 0, 0) 0%,
            rgba(0, 0, 0, 0.56) 100%
          );
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}
</style>
