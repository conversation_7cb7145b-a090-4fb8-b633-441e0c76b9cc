<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="icon" href="<%= BASE_URL %>favicon.ico" />
    <title>主页装修</title>
    <script src="./config/config.js"></script>
    <!-- <script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/5/tinymce.min.js" referrerpolicy="origin"></script> -->
    <script src="tinymce.min.js" referrerpolicy="origin"></script>
  </head>
  <body>
    <noscript>
      <strong
        >We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work
        properly without JavaScript enabled. Please enable it to
        continue.</strong
      >
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
    <!-- svg -->
    <svg
      id="symbolSvg"
      aria-hidden="true"
      style="position: absolute; width: 0px; height: 0px; overflow: hidden"
    ></svg>
    <script>
      // document.body.insertAdjacentHTML('beforeend',`<svg
      //   id="symbolSvg"
      //   aria-hidden="true"
      //   style="position: absolute; width: 0px; height: 0px; overflow: hidden"
      // ></svg>`)
      const svgIds = [
        'articlemanage',
        'graphicnavigation',
        'pictureads',
        'productgroup',
        'segmentation',
        'magiccube',
        'globalsearch',
        'productWorkshop',
        'strategyCustomization',
        'destinationSelection',
        'agent',
        'topimg',
        'titletext',
        'swiper_type_0',
        'swiper_type_1',
        'swiper_type_2',
        'swiper_type_3',
        'nav_type_0',
        'nav_type_1',
        'product_type_0',
        'product_type_1',
        'article_type_0',
        'article_type_1',
        'article_type_2',
        'cube_type_0',
        'cube_type_1',
        'cube_type_2',
        'cube_type_3',
        'cube_type_4',
        'cube_type_5',
        'cube_type_6',
        'cube_type_7',
        'qrcode',
      ]
      for (const id of svgIds) {
        fetch('svg/' + id + '.svg').then((res) => {
          res.text().then((data) => {
            const symbolData =
              '<symbol id="' +
              id +
              '"' +
              data.slice(data.indexOf('<svg') + 4, data.indexOf('</svg>')) +
              '</symbol>'
            document.getElementById('symbolSvg').innerHTML += symbolData
          })
        })
      }
    </script>
  </body>
</html>
