<?xml version="1.0" encoding="UTF-8"?>
<svg width="79px" height="63px" viewBox="0 0 79 63" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 7备份</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="横向滑动" transform="translate(-138.000000, -475.000000)" fill-rule="nonzero">
            <g id="编组备份-4" transform="translate(138.000000, 475.000000)">
                <path d="M5.26666667,0 L73.7333333,0 C76.642033,0 79,2.35050506 79,5.25 L79,57.75 C79,60.6494949 76.642033,63 73.7333333,63 L5.26666667,63 C2.35796698,63 0,60.6494949 0,57.75 L0,5.25 C0,2.35050506 2.35796698,0 5.26666667,0 Z" id="路径" fill="#BED4F1"></path>
                <polygon id="路径" fill="#BED4F1" points="5.26666667 5.25 5.26666667 57.75 73.7333333 57.75 73.7333333 5.25"></polygon>
                <path d="M10.5333333,15.75 C10.5333333,18.6494949 12.8913003,21 15.8,21 C18.7086997,21 21.0666667,18.6494949 21.0666667,15.75 C21.0666667,12.8505051 18.7086997,10.5 15.8,10.5 C12.8913003,10.5 10.5333333,12.8505051 10.5333333,15.75 Z" id="路径" fill="#FFFFFF"></path>
                <path d="M10.5333333,52.5 L20.6361167,37.3944375 C20.7435516,37.2340083 20.9163503,37.1288192 21.1085913,37.1068244 C21.3008324,37.0848296 21.4930455,37.1482568 21.63415,37.28025 L31.3840667,46.3929375 C31.4389226,46.4443682 31.5132429,46.4699267 31.5882425,46.4631529 C31.663242,46.4563792 31.7317438,46.4179213 31.7764333,46.3575 L48.1412833,24.39675 C48.2710416,24.2228065 48.4785964,24.1239566 48.6958761,24.1326203 C48.9131557,24.141284 49.112127,24.2563434 49.2275333,24.4400625 L55.1657,33.9110625 C55.240499,34.0308824 55.3969494,34.0703061 55.5198833,34.0003125 L63.6963833,29.34225 C63.8833615,29.2358887 64.1106958,29.2273772 64.305151,29.3194577 C64.4996061,29.4115381 64.6366123,29.5925759 64.6720333,29.80425 L68.4666667,52.5 L10.5333333,52.5 Z" id="路径" fill="#FFFFFF"></path>
            </g>
        </g>
    </g>
</svg>