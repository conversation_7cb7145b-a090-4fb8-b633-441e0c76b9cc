<template>
  <div class="decorate">
    <!-- 表单 -->
    <el-form label-width="80px" :model="datas" :rules="rules">
      <titlecom title="位置及尺寸" tips="可拖拽或拉伸组件，调整位置及尺寸" />
      <el-form-item label="横向位置">
        <el-slider
          v-model="datas.left"
          :min="0"
          :max="pageSetup.width"
          show-input
        ></el-slider>
      </el-form-item>
      <el-form-item label="纵向位置">
        <el-slider
          v-model="datas.top"
          :min="0"
          :max="pageSetup.height"
          show-input
        ></el-slider>
      </el-form-item>
      <el-form-item label="组件宽度">
        <el-slider
          v-model="datas.width"
          :min="0"
          :max="700"
          show-input
        ></el-slider>
      </el-form-item>
      <!-- <el-form-item label="组件高度">
        <el-slider
          v-model="datas.height"
          :min="0"
          :max="700"
          show-input
        ></el-slider>
      </el-form-item> -->
      <el-form-item label="文本排列">
        <el-radio-group v-model="datas.columnNum" class="radi1">
          <el-radio label="1">一列文本</el-radio>
          <el-radio label="2">两列文本</el-radio>
          <el-radio label="3">三列文本</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="对齐方式">
        <el-radio-group v-model="datas.alignType" class="radi1">
          <el-radio label="left">左对齐</el-radio>
          <el-radio label="center">居中</el-radio>
          <el-radio label="right">右对齐</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="行间距">
        <el-slider
          v-model="datas.lineSpacing"
          :min="0"
          :max="10"
          show-input
        ></el-slider>
      </el-form-item>
      <titlecom
        title="字段设置"
        tips="拖拽可调整排序，实际打印以商品所含字段为准！"
      />
      <div class="z-table">
        <div class="z-table-line">
          <div class="z-checkout"></div>
          <div class="z-fields">字段</div>
          <div class="z-title">打印标题</div>
          <div class="z-fontsize">字号</div>
        </div>
        <vuedraggable
          :list="datas.tableData"
          item-key="index"
          :forceFallback="true"
          :animation="200"
        >
          <template #item="{ element }">
            <div>
              <section class="z-table-line">
                <div class="z-checkout">
                  <el-checkbox
                    :disabled="element.disabled"
                    v-model="element.show"
                    size="large"
                  />
                </div>
                <div class="z-fields">{{ element.title }}</div>
                <div class="z-title">
                  <el-switch v-model="element.showTitle"> </el-switch>
                </div>
                <div class="z-fontsize">
                  <el-input-number
                    v-model="element.fontSize"
                    :min="1"
                    :max="10"
                    size="small"
                  />
                </div>
              </section>
              <section
                v-if="element.isTagline && element.show"
                class="z-table-line"
              >
                <div class="z-checkout"></div>
                <div style="width: 100px">提示语内容</div>
                <div>
                  <el-input
                    type="textarea"
                    v-model="element.value"
                    :maxlength="10"
                    show-word-limit
                    size="small"
                  />
                </div>
              </section>
            </div>
          </template>
        </vuedraggable>
      </div>
    </el-form>
  </div>
</template>

<script>
import vuedraggable from 'vuedraggable' //拖拽组件
export default {
  name: 'textblockstyle',
  props: {
    datas: Object,
    pageSetup: Object,
  },
  data() {
    return {
      rules: {
        // 校验表单输入
        name: [
          // 页面名称
          { required: true, message: '请输入页面名称', trigger: 'blur' },
        ],
        details: [
          // 页面描述
          { required: true, message: '请输入页面描述', trigger: 'blur' },
        ],
      },
      pickeShow: false, // 颜色选择器是否显示
      predefineColors: [
        // 颜色选择器预设
        'rgba(249, 249, 249, 10)',
      ],
    }
  },
  setup() {
    return {}
  },
  methods: {
    handleSelectionChange(val) {
      // 表格选择
      this.datas.tableData.forEach((item) => {
        item.show = val.some((e) => e.title === item.title)
      })
    },
  },
  components: { vuedraggable },
}
</script>

<style scoped lang="less">
.z-table {
  .z-table-line {
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #ebeef5;
    .z-checkout {
      width: 40px;
    }
    .z-fields {
      width: 60px;
    }
    .z-title {
      width: 80px;
    }
    .z-fontsize {
      width: 80px;
    }
  }
}
</style>
