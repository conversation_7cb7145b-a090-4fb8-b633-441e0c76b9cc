<template>
  <div
    class="titletext"
    :class="datas.partingLine === 1 ? 'lineee' : ''"
    :style="{
      padding: `0 ${datas.pageMargin}px`,
      backgroundColor: datas.bgColor,
    }"
  >
    <div
      class="title"
      :style="{
        justifyContent: datas.position === 'left' ? 'space-between' : 'center',
      }"
    >
      <span
        :style="{
          fontSize: datas.titleSize + 'px',
          fontWeight: datas.titleWeight === 'bold' ? 'bold' : 'normal',
          color: datas.titleColor,
        }"
        >{{ datas.title }}</span
      >
      <span
        v-show="datas.position === 'left' && datas.lookMore === 1"
        :style="{
          // color: datas.lookMoreColor,
          'font-size': '14px',
        }"
        >{{ datas.lookMoreText }}</span
      >
    </div>
    <div
      class="des"
      :style="{
        textAlign: datas.position === 'center' ? 'center' : 'left',
        fontSize: datas.desSize + 'px',
        fontWeight: datas.desWeight === 'bold' ? 'bold' : 'normal',
        color: datas.desColor,
      }"
    >
      {{ datas.description }}
    </div>
    <div
      v-show="datas.position === 'center' && datas.lookMore === 1"
      class="more"
      :style="{
        textAlign: 'center',
      }"
    >
      {{ datas.lookMoreText }}
    </div>
    <!-- 删除组件 -->
    <slot name="deles" />
  </div>
</template>

<script>
export default {
  name: 'titletext',
  props: {
    datas: Object,
  },
  data() {
    return {
      swiperIndex: 0,
      timeId: null,
      defaultImg: require('@/assets/images/slideshow.svg'),
    }
  },
  methods: {},
  created() {},
  watch: {},
}
</script>

<style scoped lang="less">
.lineee {
  &::after {
    position: absolute;
    top: 0;
    left: 0;
    box-sizing: border-box;
    width: 200%;
    height: 200%;
    transform: scale(0.5);
    transform-origin: 0 0;
    content: '';
    pointer-events: none;
    border-bottom: 1px solid #eee;
  }
}
.titletext {
  position: relative;
  min-height: 20px;
  padding: 16px 0;

  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    font-weight: 500;
    color: #333;
    .more {
    }
  }
  .des {
    font-size: 12px;
    color: #666;
  }
}
</style>
