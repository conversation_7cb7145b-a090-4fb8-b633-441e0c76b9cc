<template>
  <div
    class="articlemanage"
    :style="{
      paddingLeft: conf.pageMargin,
      paddingRight: conf.pageMargin,
    }"
  >
    <div v-if="conf.text || conf.moreType == 0" class="articlemanage-title">
      <div class="title">{{ conf.text }}</div>
      <div class="more" v-if="conf.moreType === '0'">查看更多</div>
    </div>
    <!-- 大图模式 -->
    <template v-if="datas.articleType === 'large'">
      <div
        v-for="(item, index) in articleList.slice(0, 1)"
        :key="index"
        class="large-articl"
        :style="{
          backgroundColor: '#F3F5F6',
          background: `url(${item.publicizeImgUrl}) no-repeat center/cover`,
          boxShadow: conf.boxShadow,
          outline: conf.border,
          borderRadius: conf.borderRadius,
          marginTop: index === 0 ? 0 : conf.imageMargin,
        }"
      >
        <div
          class="large-box"
          :style="{
            background: conf.titleBg,
            color: conf.titleColor,
            fontWeight: conf.textType,
          }"
        >
          <div class="large-title">
            {{ item.articleName }}
          </div>
          <div
            class="large-date"
            :style="{
              color: conf.timeColor,
            }"
          >
            {{ item.createTime }}
          </div>
        </div>
      </div>
    </template>
    <!-- 左右列表 -->
    <template v-if="conf.articleType === 'around'">
      <div
        :style="{
          boxShadow: conf.boxShadow,
          outline: conf.border,
          borderRadius: conf.borderRadius,
          marginTop: index === 0 ? 0 : conf.imageMargin,
        }"
        v-for="(item, index) in articleList.slice(0, 2)"
        :key="index"
        class="card-box"
      >
        <div class="card-box__left">
          <div
            class="card-box__title"
            :style="{
              fontWeight: conf.textType,
            }"
          >
            {{ item.articleName }}
          </div>
          <div class="card-box__date">
            {{ item.createTime }}
          </div>
          <div class="card-box__info">
            <template v-if="conf.authorSwitch">
              <img class="user-icon" src="@/assets/images/user.svg" />
              <div class="user-name">{{ item.articleAuthor }}</div>
            </template>
            <template v-if="conf.viewsSwitch">
              <img class="eye" src="@/assets/images/eye.svg" />
              <div class="eye-num">{{ item.readCount }}</div>
            </template>
          </div>
        </div>
        <img
          class="card-box__right"
          :src="item.publicizeImgUrl"
          :style="{
            background: '#F3F5F6',
            objectFit: 'cover',
          }"
        />
      </div>
    </template>
    <!-- 瀑布流 -->
    <template v-if="datas.articleType === 'flow'">
      <div class="waterfall">
        <div
          class="column"
          v-for="(col, index) in articleTwolist.slice(0, 4)"
          :key="index"
        >
          <!-- 第一列的项 -->
          <div
            v-for="(item, i) in col"
            :key="i"
            class="waterfall-box"
            :style="{
              overflow: 'hidden',
              marginTop: i && conf.imageMargin,
              boxShadow: conf.boxShadow,
              outline: conf.border,
              borderRadius: conf.borderRadius,
            }"
          >
            <img
              class="waterfall-bg"
              :style="{
                height: item.imgHeight,
                background: '#F3F5F6',
              }"
              :src="item.publicizeImgUrl"
              alt=""
            />
            <div class="waterfall-ttt">
              <div
                class="waterfall-title"
                :style="{
                  fontWeight: conf.textType,
                  // whiteSpace: 'nowrap',
                  // overflow: 'hidden',
                  // textOverflow: 'ellipsis',
                }"
              >
                {{ item.articleName }}
              </div>
              <div class="waterfall-name">
                <template v-if="conf.authorSwitch">
                  <img src="@/assets/images/user.svg" alt="" />
                  <span>{{ item.articleAuthor }}</span>
                </template>
                <template v-if="conf.viewsSwitch">
                  <img src="@/assets/images/eye.svg" alt="" />
                  <span>{{ item.readCount }}</span>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <div
      v-if="conf.moreType === '1'"
      style="
        font-size: 13px;
        line-height: 25px;
        text-align: center;
        margin-top: 10px;
        color: #808087;
      "
      @tap="toPage"
    >
      查看更多
    </div>

    <!-- 删除组件 -->
    <slot name="deles" />
  </div>
</template>

<script>
import api from '@/api/api'
const defaultImg = require('@/assets/images/default_icon.svg')
// URL 传参
const obj = {}
location.href
  .split('?')[1]
  .split('&')
  .map((item) => {
    obj[item.split('=')[0]] = item.split('=')[1]
  })
const defaultValue = ['64px', '128px', '128px', '64px'].map((imgHeight) => ({
  articleAuthor: '张三',
  readCount: '33k',
  imgHeight,
  publicizeImgUrl: defaultImg,
  articleName: '标题',
  createTime: '2023.11.12',
}))
export default {
  name: 'articlemanage',
  props: {
    datas: Object,
  },
  created() {},
  data: () => ({
    articleList: [],
  }),
  watch: {
    'datas.sourceType': {
      immediate: true,
      handler(articleTypeList) {
        api
          .getArticleH5HomeList({
            storeId: obj.storeId,
            articleTypeList,
          })
          .then((res) => {
            console.log(res)
            if (res.data.length) {
              this.articleList = res.data.map((item) => ({
                ...item,
                publicizeImgUrl:
                  process.env.VUE_APP_IMG_HOST + item.publicizeImgUrl,
              }))
            } else {
              this.articleList = defaultValue
            }
          })
          .catch(() => {
            this.articleList = defaultValue
          })
      },
    },
  },
  computed: {
    conf() {
      const style = {}
      const config = this.datas
      console.log(config)

      // 卡片样式
      if (config.cardType === 'white') {
        // 无底白边
        style.titleBg = '#fff'
      } else if (config.cardType === 'projection') {
        // 卡片投影
        style.titleBg = '#fff'
        style.boxShadow = '0px 1.5px 3px 1.5px rgba(209,209,209,0.5)'
      } else if (config.cardType === 'outline') {
        // 描边白底
        style.titleBg = '#fff'
        style.border = '1px solid #eee'
      } else if (config.cardType === 'transparent') {
        // 透明底
        style.titleBg =
          'linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.52) 100%)'
        style.titleColor = '#fff'
        style.timeColor = '#D6D6D6'
      }
      // 文字样式
      if (config.textType === 'bold') {
        // 加粗
        style.textType = 'bold'
      } else {
        // 常规
        style.textType = 'normal'
      }
      // 图片圆角
      if (config.borderRadius) {
        style.borderRadius = config.borderRadius + 'px'
      }
      // 图片间距
      if (config.imageMargin) {
        style.imageMargin = config.imageMargin + 'px'
      }
      // 页面边距
      if (config.pageMargin) {
        style.pageMargin = config.pageMargin + 'px'
      }

      // 模板
      return {
        ...config,
        ...style,
      }
    },
    articleTwolist() {
      const list = []
      for (let i = 0; i < this.articleList.length; i++) {
        // 偶数添加到第一列
        if (i % 2 === 0) {
          list[0] = list[0] || []
          list[0].push(this.articleList[i])
        } else {
          // 奇数添加到第二列
          list[1] = list[1] || []
          list[1].push(this.articleList[i])
        }
      }
      return list
    },
  },
}
</script>

<style scoped lang="less">
.articlemanage {
  position: relative;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.large-articl {
  overflow: hidden;
  height: 194px;
  position: relative;
  transition: background-position 0.2s;
  .large-box {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    color: #fff;
    padding: 4px 10px;
    color: #14131f;
    font-size: 15px;
    .large-date {
      font-size: 12px;
      font-weight: normal;
    }
  }
}
.articlemanage-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #14131f;
  margin-bottom: 8px;
  .title {
    font-size: 18px;
    font-weight: bold;
    line-height: 25px;
  }
  .more {
    font-size: 13px;
    line-height: 25px;
    color: #808087;
  }
}

.card-box {
  display: flex;
  overflow: hidden;
  height: 107px;
  border-radius: 6px;
  .card-box__left {
    background-color: #fff;
    display: flex;
    flex-direction: column;
    padding: 6px 20px 6px 10px;
    flex: 1;
    .card-box__title {
      font-size: 15px;
      font-weight: 500;
      color: #090909;
      line-height: 21px;
      width: 396 98px;
      max-height: 42px;
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .card-box__date {
      margin-top: 5px;
      font-size: 12px;
      color: rgba(20, 19, 31, 0.5);
    }
    .card-box__info {
      margin-top: auto;
      display: flex;
      align-items: center;
      .user-icon {
        width: 18px;
        height: 18px;
        border-radius: 50%;
        object-fit: contain;
      }
      .user-name {
        margin-left: 4px;
        font-size: 12px;
        font-weight: 400;
        color: #14131f;
        margin-right: 20px;
      }
      .eye {
        width: 16px;
        height: 16px;
        object-fit: contain;
      }
      .eye-num {
        font-size: 12px;
        opacity: 0.5;
        font-weight: 400;
        color: #14131f;
      }
    }
  }
  .card-box__right {
    width: 131px;
    height: 107px;
  }
}

.waterfall {
  display: flex;
  justify-content: space-between;
  .column {
    flex: 1; /* 使两列平均分配容器宽度 */
    &:first-child {
      margin-right: 10px;
    }
  }
}

.waterfall-box {
  background-color: #fff;
  overflow: hidden;
  .waterfall-bg {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
  .waterfall-ttt {
    padding: 7px 10px;
  }
  .waterfall-title {
    font-size: 14px;

    font-weight: 500;

    color: #14131f;
  }
  .waterfall-name {
    margin-top: 8px;
    display: flex;
    align-items: center;
    span {
      margin-right: 20px;
    }
    img {
      margin-right: 4px;
      width: 18px;
      height: 18px;
    }
  }
}
</style>
