<template>
  <div class="titlecom">
    <span v-if="title">{{ title }}</span>
    <p v-if="tips">{{ tips }}</p>
  </div>
</template>

<script>
export default {
  name: 'titlecom',
  props: {
    title: String,
    tips: String,
  },
}
</script>

<style lang="less" scoped>
.titlecom {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
  > span {
    padding-left: 9px;
    font-weight: bold;
    font-size: 16px;
    color: #000;
    position: relative;
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      transform: translateY(-50%);
      width: 3px;
      height: 16px;
      background: #1890ff;
    }
  }
  > p {
    color: #969799;
    font-size: 12px;
    margin-top: 5px;
  }
}
</style>
