<template>
  <div class="rightslidercom">
    <!-- 标题 -->
    <h2>{{ title }}</h2>
    <div><slot /></div>
  </div>
</template>

<script>
export default {
  name: 'rightslidercom',
  props: {
    title: String,
  },
}
</script>

<style scoped lang="less">
/* 设置组件 */
.rightslidercom {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  h2 {
    width: 100%;
    height: 48px;
    padding: 0 20px;
    font-size: 18px;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.85);
    line-height: 48px;
    border-bottom: 1px solid #ebeef5;
  }
  > div {
    padding: 20px;
    height: 0;
    flex: 1;
    overflow: auto;
  }
}
</style>
