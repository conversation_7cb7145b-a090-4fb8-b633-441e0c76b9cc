const path = require('path') //引入 path 模块
function resolve(dir) {
  return path.join(__dirname, dir) //path.join(__dirname) 设置绝对路径
}
const local = process.env.npm_lifecycle_event.includes('serve')
process.env.NODE_ENV = local ? 'development' : 'production' // { development: '本地环境', production: '线上环境' }
module.exports = {
  publicPath: local ? '' : '/scenic/interface-designer-frontend',
  outputDir: 'dist',
  assetsDir: 'static',
  productionSourceMap: false,
  devServer: {
    // open: true,
    proxy: {
      // '/api': {
      //   target: 'http://api.aseditor.fun/',
      //   ws: true,
      //   changeOrigin: true,
      //   pathRewrite: {
      //     '^/api': '',
      //   },
      // },
      '/api': {
        target: 'https://test.shukeyun.com',
        changeOrigin: true,
        pathRewrite: {
          '^/api': '',
        },
      },
    },
  },
  chainWebpack: (config) => {
    config.resolve.alias
      .set('@', resolve('./src'))
      .set('components', resolve('./src/components'))
      .set('css', resolve('./src/assets/css'))
      .set('iconfont', resolve('./src/assets/iconfont'))
      .set('img', resolve('./src/assets/images'))
      .set('utils', resolve('./src/utils'))
  },
}
