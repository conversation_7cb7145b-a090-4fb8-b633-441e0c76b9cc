<template>
  <div>
    <img
      :style="{
        width: datas.width + 'mm',
        height: datas.height + 'mm',
      }"
      src="@/assets/images/qrcode.png"
      alt=""
    />
  </div>
</template>

<script>
export default {
  name: 'ticketcode',
  props: {
    datas: Object,
    tableData: Array,
  },
  data() {
    return {
      swiperIndex: 0,
      timeId: null,
      defaultImg: require('@/assets/images/slideshow.svg'),
    }
  },
  created() {},
  watch: {},
}
</script>

<style scoped lang="less">
.textblock {
  display: flex;
  flex-wrap: wrap;
  word-break: break-all;
  .textblock-item {
    padding: 10px;
  }
}
</style>
