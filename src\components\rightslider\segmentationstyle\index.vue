<template>
  <div class="segmentationstyle">
    <!-- 表单 -->
    <el-form label-width="80px" label-position="left" :model="datas">
      <!-- 分割类型 -->
      <el-form-item label="分割类型">
        <el-radio-group v-model="datas.type">
          <el-radio label="blank">辅助空白</el-radio>
          <el-radio label="line">辅助线</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- 空白高度 -->
      <el-form-item label="空白高度">
        <el-slider
          v-model="datas.blankHeight"
          :max="100"
          show-input
        ></el-slider>
      </el-form-item>
      <el-form-item label="选择样式" v-show="datas.type == 'line'">
        <el-select
          style="width: 100%"
          v-model="datas.pattern"
          placeholder="请选择"
        >
          <el-option
            v-for="(value, key) in patternList"
            :key="key"
            :value="key"
            :label="value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-show="datas.type === 'line'" label="页面边距">
        <el-slider v-model="datas.pageMargin" :max="30" show-input></el-slider>
      </el-form-item>
      <!-- 背景颜色 -->
      <el-form-item v-show="datas.type == 'line'" label="辅助线颜色">
        <el-color-picker
          v-model="datas.color"
          show-alpha
          :predefine="predefineColors"
        >
        </el-color-picker>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'segmetnationstyle',
  props: {
    datas: Object,
  },
  data() {
    return {
      // 切换间隔
      patternList: { solid: '实线', dashed: '虚线', dotted: '点线' },
      predefineColors: [
        // 颜色选择器预设
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585',
        '#409EFF',
        '#909399',
        '#C0C4CC',
        'rgba(255, 69, 0, 0.68)',
        'rgb(255, 120, 0)',
        'hsv(51, 100, 98)',
        'hsva(120, 40, 94, 0.5)',
        'hsl(181, 100%, 37%)',
        'hsla(209, 100%, 56%, 0.73)',
        '#c7158577',
      ],
    }
  },

  created() {},

  methods: {},

  computed: {},
}
</script>
