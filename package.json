{"name": "as-editor", "version": "1.0.0", "author": "hqsk", "private": true, "scripts": {"set": "set NODE_OPTIONS=--openssl-legacy-provider", "serve:dev": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve --mode dev", "serve:test": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve --mode test", "serve:canary": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve --mode canary", "build:dev": "vue-cli-service build --mode dev", "build:test": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build --mode test", "build:canary": "vue-cli-service build --mode canary", "build:master": "vue-cli-service build --mode prod", "lint": "eslint --ext .js,.vue src", "prepare": "husky"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@tinymce/tinymce-vue": "^4.0.5", "axios": "^0.25.0", "clipboard": "^2.0.10", "core-js": "^3.21.0", "cos-js-sdk-v5": "^1.4.6", "element-plus": "^2.8.0", "file-saver": "^2.0.5", "html2canvas": "1.0.0-alpha.9", "nprogress": "^0.2.0", "qrcode": "^1.4.4", "qs": "^6.9.1", "tinymce": "^5.10.3", "vant": "^3.4.4", "vue": "^3.2.30", "vue-router": "^4.0.0-0", "vuedraggable": "^4.1.0", "vuex": "^4.0.0-0"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.2.30", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-standard": "^5.1.2", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-import": "^2.25.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^7.0.0", "husky": "^9.1.7", "less": "^3.0.4", "less-loader": "^5.0.0", "prettier": "^2.2.1", "swiper": "^5.3.6", "vue-template-compiler": "^2.6.14"}, "browserslist": ["> 1%", "last 2 versions"]}