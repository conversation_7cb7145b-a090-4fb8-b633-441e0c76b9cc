<template>
  <div class="pictureads">
    <!-- 默认 -->
    <div class="default" v-if="!getImageList[0]">
      <img src="@/assets/images/slideshow.svg" />
      <span>点击编辑图片广告</span>
    </div>
    <!-- 组件 -->
    <div
      class="module"
      v-else
      :style="
        datas.swiperType == 2
          ? {
              borderRadius: datas.borderRadius + 'px',
              margin: `${datas.moduleMargin < 0 ? 0 : datas.moduleMargin}px ${
                datas.pageMargin
              }px ${datas.moduleMargin}px`,
              boxShadow:
                datas.pictureType == 'projection'
                  ? '0px 3px 6px 3px rgba(209,209,209,0.5)'
                  : '',
            }
          : {
              padding:
                datas.moduleMargin > 0 ? `${datas.moduleMargin}px 0` : '',
              margin:
                datas.moduleMargin < 0 ? `0 0 ${datas.moduleMargin}px` : '',
            }
      "
    >
      <div class="swiperBg" v-if="datas.swiperType == 2">
        <img
          v-for="(item, index) in getImageList"
          :key="index"
          :src="item.srcBg"
          :class="{ active: index == swiperIndex }"
          :style="{
            display: item?.srcBg ? 'block' : 'none',
            objectFit: datas.aspectType == 'fit' ? 'contain' : 'cover',
          }"
        />
      </div>

      <!-- <div class="search-box">
        <img class="search-icon" src="@/assets/images/search.png" alt="" />
        <span class="search-text">搜索门票·景点·攻略·资讯</span>
      </div> -->
      <div
        class="swiper"
        :style="{
          flexDirection: datas.swiperType == 0 ? 'column' : 'row',
          gap:
            datas.swiperType == 2
              ? ''
              : datas.swiperType == 1 || datas.swiperType == 2
              ? datas.pageMargin + 'px'
              : datas.imageMargin + 'px',
          margin: datas.swiperType == 2 ? '' : `0 ${datas.pageMargin}px`,
          transform:
            datas.swiperType == 3
              ? `translateX(${
                  375 / 2 -
                  datas.pageMargin -
                  ((375 - datas.pageMargin * 2 + datas.imageMargin) *
                    getImageList.length -
                    datas.imageMargin) /
                    2
                }px)`
              : `translateX(${
                  (datas.swiperType == 2
                    ? datas.pageMargin * 2 - 375
                    : datas.pageMargin - 375) * swiperIndex
                }px)`,
        }"
      >
        <img
          v-for="(item, index) in getImageList"
          :key="index"
          :src="item.src || defaultImg"
          :style="{
            boxShadow:
              datas.pictureType == 'projection' && datas.swiperType != 2
                ? '0px 3px 6px 3px rgba(209,209,209,0.5)'
                : '',
            objectFit:
              datas.swiperType == 2
                ? 'contain'
                : datas.aspectType == 'fit'
                ? 'contain'
                : 'cover',
            borderRadius:
              datas.swiperType == 2 ? '' : datas.borderRadius + 'px',
            width:
              datas.swiperType == 3 && datas.screenType == 1 ? '50%' : '100%',
            height:
              datas.swiperType == 3 && datas.screenType == 1
                ? '105.5px'
                : '211px',
          }"
        />
      </div>
    </div>
    <!-- 指示器 -->
    <div v-if="datas.swiperType == 1 || datas.swiperType == 2">
      <div
        v-if="datas.indicatorType < 2"
        :class="`tips tips${datas.indicatorType}`"
      >
        <div
          v-for="(_, index) in getImageList"
          :key="index"
          :class="{ active: swiperIndex == index }"
        ></div>
      </div>
      <div v-else :class="`tips${datas.indicatorType}`">
        {{ `${swiperIndex + 1}/${getImageList.length}` }}
      </div>
    </div>
    <!-- 删除组件 -->
    <slot name="deles" />
  </div>
</template>

<script>
export default {
  name: 'pictureads',
  props: {
    datas: Object,
  },
  data() {
    return {
      swiperIndex: 0,
      timeId: null,
      defaultImg: require('@/assets/images/slideshow.svg'),
    }
  },
  methods: {
    auto() {
      if (
        (this.datas.swiperType == 1 || this.datas.swiperType == 2) &&
        this.datas.autoSwitch
      ) {
        this.timeId = setTimeout(() => {
          this.swiperIndex +=
            this.swiperIndex < this.getImageList.length - 1
              ? 1
              : -this.swiperIndex
          this.auto()
        }, [3000, 4000, 5000][this.datas.durationType])
      } else {
        this.swiperIndex = 0
      }
    },
  },
  created() {
    this.auto()
  },
  computed: {
    getImageList() {
      return this.datas.imageList.filter((item) => item.src)
    },
  },
  watch: {
    'datas.autoSwitch'() {
      clearTimeout(this.timeId)
      this.auto()
    },
    'datas.swiperType'() {
      clearTimeout(this.timeId)
      this.auto()
    },
  },
}
</script>

<style scoped lang="less">
.pictureads {
  position: relative;
  .default {
    width: 100%;
    height: 211px;
    background: #f3f5f6;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    > img {
      width: 148px;
    }
    > span {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.5);
    }
  }
  .module {
    position: relative;
    overflow: hidden;
    .swiperBg {
      > img {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        width: 100%;
        height: 100%;
        transition: 1s;
        opacity: 0;
      }
      .active {
        opacity: 1;
      }
    }
    .swiper {
      position: relative;
      display: flex;
      transition: 0.3s;
      > img {
        flex: none;
      }
    }
  }
  .tips {
    position: absolute;
    bottom: 7.5px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 5px;
    > div {
      background: #d8d8d8;
      transition: 0.2s;
    }
    .active {
      background: #fff;
    }
  }
  .tips0 {
    > div {
      width: 8px;
      height: 8px;
      border-radius: 4px;
    }
    .active {
      width: 17px;
    }
  }
  .tips1 {
    > div {
      width: 17px;
      height: 6px;
    }
  }
  .tips2,
  .tips3 {
    position: absolute;
    right: 10px;
    bottom: 7.5px;
    width: 27px;
    height: 15px;
    text-align: center;
    line-height: 15px;
    border-radius: 15px;
    font-size: 11px;
    color: #fff;
    transition: 0.2s;
  }
  .tips3 {
    background: rgba(2, 2, 2, 0.52);
  }
  .search-box {
    position: absolute;
    top: 10px;
    width: 90%;
    height: 30px;
    left: 5%;
    background: #ffffff;
    border-radius: 30px;
    z-index: 11;
    display: flex;
    align-items: center;
    .search-icon {
      width: 24px;
      height: 25px;
      margin-left: 10px;
    }
    .search-text {
      color: #999999;
      margin-left: 8px;
    }
  }
}
</style>
