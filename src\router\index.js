import { createRouter, createWebHashHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'layout',
    redirect: '/home',
    component: () => import('@/layout'),
    children: [
      {
        //  首页
        path: '/home',
        name: 'home',
        component: () => import('@/layout/home'),
      },
      {
        //  门票设计
        path: '/ticket',
        name: 'ticket',
        component: () => import('@/layout/ticket'),
      },
    ],
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes,
})
export default router
