<template>
  <div class="pictureWithQuestion">
    <div class="imageWrapper">
      <img :src="datas.imgUrl || defaultImg" alt="默认图片" draggable="false" />
    </div>
    <div v-if="datas.isShowQuestion" class="questionWrapper">
      <span class="leftText">"我想去北京旅行"</span>
      <div class="confrimBtn">问一问</div>
    </div>

    <!-- 删除组件 -->
    <slot name="deles" />
  </div>
</template>

<script>
export default {
  name: 'topimg',
  props: {
    datas: Object,
  },
  data() {
    return {
      defaultImg:
        'https://prod.shukeyun.com/maintenance/deepfile/data/2025-08-15/upload_ccbfa918ded29e8b46ac5314e114ab59.png', // 有问一问
    }
  },
}
</script>

<style scoped lang="less">
.pictureWithQuestion {
  position: relative;
  text-align: center;
  padding: 0;
  background: #fff;
  .imageWrapper {
    height: 210px;
    width: 100%;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .questionWrapper {
    padding: 0 6px 0 12px;
    position: absolute;
    top: 70%;
    left: 5%;
    width: 90%;
    height: 47px;
    background: rgba(115, 147, 255, 0.43);
    border-radius: 24px;
    border: 1px solid #ffffff;
    backdrop-filter: blur(1.612px);
    display: flex;
    align-items: center;
    justify-content: space-between;
    .leftText {
      color: #ffffff;
      font-size: 15px;
    }
    .confrimBtn {
      width: 69px;
      height: 36px;
      line-height: 36px;
      background: #ffffff;
      border-radius: 20px;
      opacity: 0.95;
      color: #3C6BFF;
    }
  }
}
</style>
