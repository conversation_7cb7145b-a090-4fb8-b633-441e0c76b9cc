<template>
  <div class="titletextstyle">
    <!-- 表单 -->
    <el-form label-width="80px" label-position="left" :model="datas">
      <!-- 标题内容 -->
      <el-form-item label="标题内容">
        <el-input
          v-model="datas.title"
          placeholder="请输入标题"
          :maxlength="20"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="描述内容">
        <el-input
          type="textarea"
          v-model="datas.description"
          placeholder="请输入描述"
          :maxlength="500"
          show-word-limit
        />
      </el-form-item>
      <!-- 下划线 -->
      <div class="bor" />
      <titlecom title="组件样式" />
      <!-- 显示位置 -->
      <el-form-item label="显示位置">
        <el-radio-group v-model="datas.position">
          <el-radio label="left">居左显示</el-radio>
          <el-radio label="center">居中显示</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- 标题大小 -->
      <el-form-item label="标题大小">
        <el-slider
          v-model="datas.titleSize"
          :min="12"
          :max="36"
          show-input
        ></el-slider>
      </el-form-item>
      <!-- 描述大小 -->
      <el-form-item label="描述大小">
        <el-slider
          v-model="datas.desSize"
          :min="12"
          :max="24"
          show-input
        ></el-slider>
      </el-form-item>
      <!-- 页面边距 -->
      <el-form-item label="页面边距">
        <el-slider v-model="datas.pageMargin" :max="30" show-input> </el-slider>
      </el-form-item>
      <!-- 标题加粗 -->
      <el-form-item label="标题加粗">
        <el-radio-group v-model="datas.titleWeight">
          <el-radio label="routine">常规</el-radio>
          <el-radio label="bold">加粗</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- 描述粗细 -->
      <el-form-item label="描述粗细">
        <el-radio-group v-model="datas.desWeight">
          <el-radio label="routine">常规</el-radio>
          <el-radio label="bold">加粗</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- 标题颜色 -->
      <el-form-item label="标题颜色">
        <el-color-picker
          v-model="datas.titleColor"
          show-alpha
          :predefine="predefineColors"
        >
        </el-color-picker>
      </el-form-item>
      <!-- 描述颜色 -->
      <el-form-item label="描述颜色">
        <el-color-picker
          v-model="datas.desColor"
          show-alpha
          :predefine="predefineColors"
        >
        </el-color-picker>
      </el-form-item>
      <!-- 背景颜色 -->
      <el-form-item label="背景颜色">
        <el-color-picker
          v-model="datas.bgColor"
          show-alpha
          :predefine="predefineColors"
        >
        </el-color-picker>
      </el-form-item>
      <!-- 底部分割线 -->
      <el-form-item label="底部分割线" label-width="88px" class="w-88">
        <el-radio-group v-model="datas.partingLine">
          <el-radio :label="0">不显示</el-radio>
          <el-radio :label="1">显示</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- 查看更多 -->
      <el-form-item label="查看更多" class="w-88">
        <el-radio-group
          style="flex: none; margin-right: 10px"
          v-model="datas.lookMore"
        >
          <el-radio :label="0">不显示</el-radio>
          <el-radio :label="1">显示</el-radio>
        </el-radio-group>
        <el-input
          v-show="datas.lookMore === 1"
          style="margin-top: 10px"
          v-model="datas.lookMoreText"
          placeholder="请输入"
          show-word-limit
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'titletextstyle',
  props: {
    datas: Object,
  },
  data() {
    return {
      // 查看更多
      lookMoreList: { left: '居右显示', center: '居中显示' },
      // 标题加粗
      titleWeightList: { 0: '3 秒', 1: '4 秒', 2: '5 秒' },
      predefineColors: [
        // 颜色选择器预设
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585',
        '#409EFF',
        '#909399',
        '#C0C4CC',
        'rgba(255, 69, 0, 0.68)',
        'rgb(255, 120, 0)',
        'hsv(51, 100, 98)',
        'hsva(120, 40, 94, 0.5)',
        'hsl(181, 100%, 37%)',
        'hsla(209, 100%, 56%, 0.73)',
        '#c7158577',
      ],
    }
  },

  created() {},

  methods: {},
}
</script>
