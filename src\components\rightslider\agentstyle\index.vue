<template>
  <div class="agentStyleContent">
    <el-form label-width="80px" label-position="left" :model="datas">
      <titlecom title="基本信息" />
      <el-form-item label="专题名称">
        <el-input
          v-model="datas.compName"
          placeholder="请输入"
          clearable
          :maxlength="10"
        />
      </el-form-item>
      <div class="line"></div>
      <titlecom title="智能体排序" />
      <div>
        <vuedraggable
          :list="datas.agentList"
          item-key="index"
          :forceFallback="true"
          :animation="200"
        >
          <template #item="{ element, index }">
            <section class="agList">
              <!-- 删除按钮 -->
              <van-icon class="imgClose" name="close" @click="delWord(index)" />
              <!-- 智能体信息展示 -->
              <div class="agent-info">
                <div class="leftB">
                  <el-image
                    class="agent-avatar"
                    :src="element.avatar"
                    fit="cover"
                  >
                    <!-- 加载失败占位 -->
                    <template #error>
                      <div class="image-placeholder">
                        <el-icon><User /></el-icon>
                      </div>
                    </template>
                  </el-image>
                  <div class="cutPng">更换</div>
                </div>
                <div class="agent-text">
                  <p class="agent-name">{{ element.name }}</p>
                  <p class="agent-content">{{ element.intro }}</p>
                </div>
              </div>
            </section>
          </template>
        </vuedraggable>
      </div>
      <el-button class="imgAdd" @click="addWord">
        添加智能体（{{ datas.agentList.length }}/10）
      </el-button>
      <AgentSelectionModal
        v-model:visible="showModal"
        @confirm="handleConfirm"
      />
      <div class="redAdd">请添加智能体</div>

      <div class="line"></div>
      <titlecom title="选择模板" />
      <typecom v-model="datas.swiperType" :list="list" />

      <div class="line"></div>
      <titlecom title="组件样式" />
      <el-form-item style="white-space: nowrap" label="智能体间距">
        <el-slider
          v-model="datas.agentSpacing"
          :max="30"
          show-input
        ></el-slider>
      </el-form-item>
      <el-form-item label="页面边距">
        <el-slider v-model="datas.pageMargin" :max="30" show-input></el-slider>
      </el-form-item>
      <el-form-item label="浏览人数">
        <el-switch
          v-model="datas.visitorsShow"
          inline-prompt
          active-text="展示"
          inactive-text="不展示"
        />
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import AgentSelectionModal from './agentSelectionModal.vue'
import vuedraggable from 'vuedraggable' //拖拽组件
export default {
  components: { AgentSelectionModal, vuedraggable },
  name: 'agentstyle',
  props: {
    datas: Object,
  },
  data() {
    return {
      showModal: false,
      list: [
        {
          name: '一行一个',
          value: '0',
          icon: 'swiper_type_0',
        },
        {
          name: '一行两个',
          value: '1',
          icon: 'swiper_type_1',
        },
      ],
    }
  },
  methods: {
    handleConfirm(selectedAgents) {
      console.log('勾选的智能体：', selectedAgents)

      // 将选择的智能体添加到列表中
      selectedAgents.forEach((agent) => {
         this.datas.agentList.push(agent)
        // const exists = this.datas.agentList.some((item) => item.id === agent.id)
        // if (!exists) {
        //   this.datas.agentList.push(agent)
        // }
      })

      // 更新显示提示
      this.showModal = false
    },
    delWord(index) {
      this.datas.agentList.splice(index, 1)
    },
    // 添加热词
    addWord() {
      if (this.datas.agentList.length >= 10) {
        this.$message.warning('最多只能添加10个智能体')
        return
      }
      this.showModal = true
    },
  },
}
</script>
<style scoped lang="less">
.agentStyleContent {
  .line {
    width: 100%;
    margin: 18px 0;
    height: 1px;
    background: rgba(0, 0, 0, 0.1);
  }
  .agList {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    margin: 20px 0;
    position: relative;
    height: 112px;
    background: #ffffff;
    border-radius: 2px;
    border: 1px solid rgba(0, 0, 0, 0.15);

    // 删除按钮
    .imgClose {
      position: absolute;
      right: 10px;
      top: 10px;
      cursor: pointer;
      font-size: 19px;
      color: #999;
      &:hover {
        color: #ff4d4f;
      }
    }

    // 智能体信息（头像 + 名称 + 更换按钮）
    .agent-info {
      display: flex;
      gap: 16px;
      .leftB {
        width: 80px;
        height: 80px;
        position: relative;
        .agent-avatar {
          width: 100%;
          height: 100%;
        }

        .cutPng {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 20px;
          line-height: 20px;
          color: #fff;
          text-align: center;
          background: rgba(0, 0, 0, 0.63);
          font-size: 12px;
          cursor: pointer;
        }
      }

      .agent-text {
        width: 190px;
        display: flex;
        flex-direction: column;

        .agent-name {
          font-size: 16px;
          font-weight: 700;
          color: #14131f;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .agent-content {
          font-size: 14px;
          color: #14131f;
          margin-top: 10px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2; /* 限制显示3行 */
          overflow: hidden;
          color: #bababd;
        }
      }
    }
  }
  .imgAdd {
    width: 100%;
    height: 40px;
    border-style: dashed;
  }
  .redAdd {
    color: #ff4d4f;
    margin-top: 10px;
  }
  .liebTit {
    color: #999;
    margin-bottom: 12px;
  }
}
</style>
