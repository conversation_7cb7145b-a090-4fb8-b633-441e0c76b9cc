/**请求路径
 *
 */
import { get, post, put } from '@/api/request'
const path = '/scenic/api-v2'

export default {
  // 发布
  push: (params) => {
    return post(`${path}/store/design/add`, params)
  },
  // 详情
  info: (params) => {
    return get(`${path}/store/design/info`, params)
  },
  // 草稿详情
  checkInfo: (params) => {
    return get(`${path}/store/design/check/draft`, params)
  },
  // 修改
  update: (params) => {
    return put(`${path}/store/design/update`, params)
  },
  // 修改
  uploadImg: (params) => {
    return post(`/scenic/api-v2/aws/uploadFile`, params, {
      headers: {
        'Content-Type': 'application/json',
      },
    })
  },
  // 设置 cookie
  setCookie: (id) => {
    return put(`${path}/user/noAuth/setCookie/${id}`)
  },

  // 获取门票模板详情
  getTicketInfo: (id) => {
    return get(`${path}/ticketTemplate/getinfo?id=${id}&systemType=backend`)
  },
  // 保存门票模板
  saveTicket: (params) => {
    return post(`${path}/ticketTemplate/save`, params)
  },
  // 编辑门票模板
  updateTicket: (params) => {
    return post(`${path}/ticketTemplate/update`, params)
  },
  // 店铺详情
  storeInfo: (id) => {
    return get(`${path}/ticketStore/noAuth/store/${id}`)
  },
  // 活动页列表
  getPageList: (params) => get(`${path}/store/design/page`, params),
  // 景区列表
  getScenicList: (params) => post(`${path}/appScenic/scenicPageList`, params),
  // 门票列表
  getTicketList: (params) => get(`${path}/appScenic/ticketList`, params),
  // 组合票列表
  getComposeTicketList: (params) =>
    post(`${path}/appTicket/appComposeGoodsList`, params),
  // 权益卡列表
  getTravelTicketList: (params) =>
    get(`${path}/appScenic/appTravelGoodsPageList`, params),
  // 导览列表
  getNavigationList: (params) => get(`${path}/navigation/store/list`, params),
  // 文章列表
  getArticleList: (params) => get(`${path}/article/page`, params),
  // 文章列表（首页推荐）
  getArticleH5HomeList: (params) => get(`${path}/article/h5/home/<USER>
  // 文章列表（列表启用）
  getArticleH5List: (params) => get(`${path}/article/h5/list`, params),
  // 店铺商品列表
  getStoreGoodsList: (params) => post(`${path}/appTicket/storeDesignerGoodsList`, params),
  // 设计器商品列表
  getDesignerGoodsList: (params) => post(`${path}/appTicket/designerGoodsList`, params),
  // 智能体
  getAgentList: (params) => get(`${path}/agent/page`, params),
}
