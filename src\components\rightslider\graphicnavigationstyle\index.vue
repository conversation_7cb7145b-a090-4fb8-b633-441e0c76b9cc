<template>
  <div class="graphicnavigationstyle">
    <!-- 表单 -->
    <el-form label-width="80px" label-position="left" :model="datas">
      <!-- 选择模板 -->
      <titlecom title="选择模板" />
      <typecom v-model="datas.navigationType" :list="navigations" />
      <div class="bor" />
      <!-- 提示 -->
      <titlecom
        title="添加导航"
        tips="拖拽排序，最少 4 个，最多添加 10 个导航"
      />
      <!-- 图文导航 -->
      <div v-if="datas.navigationList[0]">
        <vuedraggable
          :list="datas.navigationList"
          item-key="index"
          :forceFallback="true"
          :animation="200"
        >
          <template #item="{ element, index }">
            <section class="imgList">
              <van-icon
                class="imgClose"
                name="close"
                @click="deleteimg(index)"
                v-if="index > 3"
              />
              <!-- 图片 -->
              <upload v-model="element.src" />
              <!-- 标题和链接 -->
              <div class="imgText">
                <el-input
                  class="input-type"
                  v-model="element.text"
                  placeholder="请输入标题"
                ></el-input>
                <!-- 选择类型 -->
                <linkcom :element="element" />
              </div>
            </section> </template
          >>
        </vuedraggable>
      </div>
      <el-button
        class="imgAdd"
        :disabled="datas.navigationList.length > 9"
        @click="uploadInformation"
      >
        添加导航（{{ datas.navigationList.length }}/10）
      </el-button>
      <!-- 下划线 -->
      <div class="bor"></div>
      <titlecom title="组件样式" />
      <!-- 图片圆角 -->
      <el-form-item label="图片圆角">
        <el-slider v-model="datas.borderRadius" :max="55" show-input>
        </el-slider>
      </el-form-item>
      <!-- 字体大小 -->
      <el-form-item label="字体大小">
        <el-slider v-model="datas.textSize" :max="50" :min="12" show-input>
        </el-slider>
      </el-form-item>
      <!-- 文字颜色 -->
      <el-form-item label="文字颜色">
        <!-- 颜色选择器 -->
        <el-color-picker
          v-model="datas.textColor"
          show-alpha
          :predefine="predefineColors"
        >
        </el-color-picker>
      </el-form-item>
      <!-- 文字高度 -->
      <el-form-item label="文字高度">
        <el-slider v-model="datas.textHeight" :max="50" :min="12" show-input>
        </el-slider>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { ElMessage } from 'element-plus'
import vuedraggable from 'vuedraggable' //拖拽组件
import upload from '../../upload' //图片上传

export default {
  name: 'graphicnavigationstyle',
  props: {
    datas: Object,
  },
  data() {
    return {
      predefineColors: [
        // 颜色选择器预设
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585',
        '#409EFF',
        '#909399',
        '#C0C4CC',
        'rgba(255, 69, 0, 0.68)',
        'rgb(255, 120, 0)',
        'hsv(51, 100, 98)',
        'hsva(120, 40, 94, 0.5)',
        'hsl(181, 100%, 37%)',
        'hsla(209, 100%, 56%, 0.73)',
        '#c7158577',
      ],
      navigations: [
        {
          name: '单行滑动',
          value: '0',
          icon: 'nav_type_0',
        },
        {
          name: '两行排列',
          value: '1',
          icon: 'nav_type_1',
        },
      ],
      emptyText: '',
      uploadImgDataType: null,
    }
  },
  created() {},
  methods: {
    showUpload(type) {
      this.uploadImgDataType = type
      this.$refs.upload.showUpload()
    },
    // 提交
    uploadInformation() {
      this.datas.navigationList.push({
        text: '',
        src: '',
        link: '',
        linkType: '0',
        linkTitle: '',
      })
    },

    // 清空背景图片
    clear() {
      this.datas.bgImg = ''
    },
    /* 删除图片列表的图片 */
    deleteimg(index) {
      if (index < 4) {
        ElMessage.warning('默认导航不可删除')
        return
      }
      this.datas.navigationList.splice(index, 1)
    },
  },
  computed: {
    styleText() {
      return this.navigations[this.datas.navigationType]
    },
  },
  components: { upload, vuedraggable },
}
</script>

<style scoped lang="less">
.graphicnavigationstyle {
  .imgList {
    padding: 10px;
    margin: 15px 0;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 0 4px 0 rgba(10, 42, 97, 0.2);
    display: flex;
    position: relative;
    gap: 10px;
    .imgClose {
      position: absolute;
      z-index: 1;
      right: 10px;
      top: 10px;
      cursor: pointer;
      font-size: 19px;
    }
    .imgText {
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      justify-content: space-between;
      .input-type {
        :deep(input) {
          border: none;
          padding: 0 24px 0 0;
          height: 21px;
          line-height: 21px;
        }
      }
    }
  }
  .imgAdd {
    width: 100%;
    height: 40px;
    border-style: dashed;
  }
}
</style>
