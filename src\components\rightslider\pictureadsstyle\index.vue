<template>
  <div class="pictureadsstyle">
    <!-- 表单 -->
    <el-form label-width="80px" label-position="left" :model="datas">
      <!-- 标题内容 -->
      <titlecom title="选择模板" />
      <!-- 轮播图选择 -->
      <typecom v-model="datas.swiperType" :list="list" />
      <!-- 下划线 -->
      <div class="bor" />
      <titlecom title="添加图片" tips="拖拽排序，最多添加 10 张图片" />
      <!-- 图片广告 -->
      <div v-if="datas.imageList[0]">
        <vuedraggable
          :list="datas.imageList"
          item-key="index"
          :forceFallback="true"
          :animation="200"
        >
          <template #item="{ element, index }">
            <section class="imgList">
              <van-icon
                class="imgClose"
                name="close"
                @click="deleteimg(index)"
              />
              <!-- 图片 -->
              <upload v-model="element.src" />
              <upload v-model="element.srcBg" v-if="datas.swiperType == 2" />
              <!-- 标题和链接 -->
              <div class="imgText">
                <span>链接设置</span>
                <linkcom :element="element" />
              </div>
            </section>
          </template>
        </vuedraggable>
      </div>
      <!-- 添加图片 -->
      <el-button
        class="imgAdd"
        :disabled="datas.imageList.length > 9"
        @click="addImg"
      >
        添加图片（{{ datas.imageList.length }}/10）
      </el-button>
      <!-- 下划线 -->
      <div class="bor"></div>
      <titlecom title="组件样式" />
      <!-- 自动轮播 -->
      <el-form-item
        label="自动轮播"
        v-show="datas.swiperType == 1 || datas.swiperType == 2"
      >
        <el-switch
          v-model="datas.autoSwitch"
          inline-prompt
          active-text="是"
          inactive-text="否"
        />
      </el-form-item>
      <!-- 切换间隔 -->
      <el-form-item
        label="切换间隔"
        v-show="datas.swiperType == 1 || datas.swiperType == 2"
      >
        <el-select
          style="width: 100%"
          v-model="datas.durationType"
          placeholder="请选择"
        >
          <el-option
            v-for="(value, key) in durations"
            :key="key"
            :value="key"
            :label="value"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- 轮播提示 -->
      <el-form-item
        label="轮播提示"
        v-show="datas.swiperType == 1 || datas.swiperType == 2"
      >
        <el-select
          style="width: 100%"
          v-model="datas.indicatorType"
          placeholder="请选择"
        >
          <el-option
            v-for="(value, key) in indicators"
            :key="key"
            :value="key"
            :label="value"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- 一屏展示 -->
      <el-form-item label="一屏展示" v-show="datas.swiperType == 3">
        <el-select
          style="width: 100%"
          v-model="datas.screenType"
          placeholder="请选择"
        >
          <el-option
            v-for="(value, key) in screenNums"
            :key="key"
            :value="key"
            :label="value"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- 图片样式 -->
      <el-form-item label="图片样式">
        <el-radio-group v-model="datas.pictureType" class="radi1">
          <el-radio label="routine">常规</el-radio>
          <el-radio label="projection">投影</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- 填充样式 -->
      <el-form-item
        label="填充样式"
        v-show="datas.swiperType == 1 || datas.swiperType == 2"
      >
        <el-radio-group v-model="datas.aspectType" class="radi1">
          <el-radio label="fill">填充</el-radio>
          <el-radio label="fit">留白</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- 图片圆角 -->
      <el-form-item label="图片圆角">
        <el-slider
          v-model="datas.borderRadius"
          :max="211"
          show-input
        ></el-slider>
      </el-form-item>
      <!-- 图片间距 -->
      <el-form-item
        label="图片间距"
        v-show="datas.swiperType == 0 || datas.swiperType == 3"
      >
        <el-slider v-model="datas.imageMargin" :max="30" show-input></el-slider>
      </el-form-item>
      <!-- 页面边距 -->
      <el-form-item label="页面边距">
        <el-slider v-model="datas.pageMargin" :max="50" show-input></el-slider>
      </el-form-item>
      <!-- 组件间距 -->
      <el-form-item label="组件间距">
        <el-slider
          v-model="datas.moduleMargin"
          :max="30"
          :min="datas.swiperType == 3 && datas.screenType == 1 ? -105 : -211"
          show-input
        ></el-slider>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import vuedraggable from 'vuedraggable' //拖拽组件
import upload from '../../upload' //图片上传

export default {
  name: 'pictureadsstyle',
  components: { vuedraggable, upload },
  props: {
    datas: Object,
  },
  data() {
    return {
      // 切换间隔
      durations: { 0: '3 秒', 1: '4 秒', 2: '5 秒' },
      // 轮播类型
      list: [
        {
          name: '一行一个',
          value: '0',
          icon: 'swiper_type_0',
        },
        {
          name: '轮播海报',
          value: '1',
          icon: 'swiper_type_1',
        },
        {
          name: '双层轮播',
          value: '2',
          icon: 'swiper_type_2',
        },
        {
          name: '横向滑动',
          value: '3',
          icon: 'swiper_type_3',
        },
      ],
      // 指示器类型
      indicators: { 0: '样式一', 1: '样式二', 2: '样式三', 3: '样式四' },
      screenNums: { 0: '1个图片', 1: '2个图片' },
      emptyText: '',
    }
  },
  methods: {
    // 添加图片
    addImg() {
      this.datas.imageList.push({
        text: '',
        src: '',
        srcBg: '',
        link: '',
        linkType: '0',
      })
    },
    // 删除图片
    deleteimg(index) {
      this.datas.imageList.splice(index, 1)
    },
  },
}
</script>

<style scoped lang="less">
.pictureadsstyle {
  .imgList {
    padding: 10px;
    margin: 15px 0;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 0 4px 0 rgba(10, 42, 97, 0.2);
    display: flex;
    position: relative;
    gap: 10px;
    .imgClose {
      position: absolute;
      right: 10px;
      top: 10px;
      cursor: pointer;
      font-size: 19px;
    }
    .imgText {
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      justify-content: space-between;
    }
  }
  .imgAdd {
    width: 100%;
    height: 40px;
    border-style: dashed;
  }
}
</style>
