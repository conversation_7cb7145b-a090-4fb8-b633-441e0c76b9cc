<?xml version="1.0" encoding="UTF-8"?>
<svg viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="辅助分割" transform="translate(-214.000000, -1436.000000)">
            <g id="编组" transform="translate(214.000000, 1436.000000)">
                <rect id="矩形备份" fill="currentColor" opacity="0" x="0" y="0" width="40" height="40"></rect>
                <rect id="矩形备份-2" stroke="currentColor" stroke-width="2" fill-opacity="0" fill="currentColor" x="6" y="27" width="28" height="8"></rect>
                <rect id="矩形备份-15" stroke="currentColor" stroke-width="2" fill-opacity="0" fill="currentColor" x="6" y="6" width="28" height="8"></rect>
                <rect id="矩形备份-4" stroke="currentColor" fill="currentColor" x="5.5" y="20.5" width="29" height="1"></rect>
            </g>
        </g>
    </g>
</svg>