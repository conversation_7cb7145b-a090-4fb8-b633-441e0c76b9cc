<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>IconFont Demo</title>
  <link rel="shortcut icon" href="https://gtms04.alicdn.com/tps/i4/TB1_oz6GVXXXXaFXpXXJDFnIXXX-64-64.ico" type="image/x-icon"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">&#xe86b;</a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=1670638" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe631;</span>
                <div class="name">三栏</div>
                <div class="code-name">&amp;#xe631;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe626;</span>
                <div class="name">上</div>
                <div class="code-name">&amp;#xe626;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe650;</span>
                <div class="name">下</div>
                <div class="code-name">&amp;#xe650;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe67b;</span>
                <div class="name">搜索框</div>
                <div class="code-name">&amp;#xe67b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b2;</span>
                <div class="name">网页</div>
                <div class="code-name">&amp;#xe6b2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe625;</span>
                <div class="name">搜索框</div>
                <div class="code-name">&amp;#xe625;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b7;</span>
                <div class="name">置顶</div>
                <div class="code-name">&amp;#xe6b7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe623;</span>
                <div class="name">点线-1-01</div>
                <div class="code-name">&amp;#xe623;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe644;</span>
                <div class="name">虚线</div>
                <div class="code-name">&amp;#xe644;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe624;</span>
                <div class="name">icon_分割线_实线</div>
                <div class="code-name">&amp;#xe624;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe620;</span>
                <div class="name">icon_无边距</div>
                <div class="code-name">&amp;#xe620;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe622;</span>
                <div class="name">icon_左右边距</div>
                <div class="code-name">&amp;#xe622;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6c3;</span>
                <div class="name">辅助线条</div>
                <div class="code-name">&amp;#xe6c3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61f;</span>
                <div class="name">辅助空白_未选中</div>
                <div class="code-name">&amp;#xe61f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe68b;</span>
                <div class="name">一左三右</div>
                <div class="code-name">&amp;#xe68b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe687;</span>
                <div class="name">二左二右</div>
                <div class="code-name">&amp;#xe687;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe688;</span>
                <div class="name">一行二个</div>
                <div class="code-name">&amp;#xe688;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe689;</span>
                <div class="name">一行四个</div>
                <div class="code-name">&amp;#xe689;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe68a;</span>
                <div class="name">一左二右</div>
                <div class="code-name">&amp;#xe68a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe649;</span>
                <div class="name">一上二下</div>
                <div class="code-name">&amp;#xe649;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe99a;</span>
                <div class="name">固定</div>
                <div class="code-name">&amp;#xe99a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe682;</span>
                <div class="name">横向滑动</div>
                <div class="code-name">&amp;#xe682;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61a;</span>
                <div class="name">列表切换-01</div>
                <div class="code-name">&amp;#xe61a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61e;</span>
                <div class="name">图片</div>
                <div class="code-name">&amp;#xe61e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6fc;</span>
                <div class="name">commodity-一行两个</div>
                <div class="code-name">&amp;#xe6fc;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6fd;</span>
                <div class="name">commodity-详细列表</div>
                <div class="code-name">&amp;#xe6fd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe619;</span>
                <div class="name">大图模式</div>
                <div class="code-name">&amp;#xe619;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe686;</span>
                <div class="name">一行三个</div>
                <div class="code-name">&amp;#xe686;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61c;</span>
                <div class="name">icon_商品图_一大两小</div>
                <div class="code-name">&amp;#xe61c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe618;</span>
                <div class="name">选择模块-导航横向滑动</div>
                <div class="code-name">&amp;#xe618;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe619;</span>
                <div class="name">一大两小</div>
                <div class="code-name">&amp;#xe619;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe648;</span>
                <div class="name">富文本</div>
                <div class="code-name">&amp;#xe648;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe615;</span>
                <div class="name">语音</div>
                <div class="code-name">&amp;#xe615;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe602;</span>
                <div class="name">积分</div>
                <div class="code-name">&amp;#xe602;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe641;</span>
                <div class="name">运营</div>
                <div class="code-name">&amp;#xe641;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe859;</span>
                <div class="name">视频</div>
                <div class="code-name">&amp;#xe859;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6a4;</span>
                <div class="name">自定义模块</div>
                <div class="code-name">&amp;#xe6a4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe657;</span>
                <div class="name">老师</div>
                <div class="code-name">&amp;#xe657;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe612;</span>
                <div class="name">优惠券</div>
                <div class="code-name">&amp;#xe612;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe621;</span>
                <div class="name">快速涨粉</div>
                <div class="code-name">&amp;#xe621;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe647;</span>
                <div class="name">拼团</div>
                <div class="code-name">&amp;#xe647;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe607;</span>
                <div class="name">秒杀</div>
                <div class="code-name">&amp;#xe607;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8c9;</span>
                <div class="name">直播 播放 摄像机 天线 线性</div>
                <div class="code-name">&amp;#xe8c9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6c5;</span>
                <div class="name">周期购</div>
                <div class="code-name">&amp;#xe6c5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe604;</span>
                <div class="name">课程</div>
                <div class="code-name">&amp;#xe604;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61d;</span>
                <div class="name">店铺信息</div>
                <div class="code-name">&amp;#xe61d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe638;</span>
                <div class="name">在线客服</div>
                <div class="code-name">&amp;#xe638;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60e;</span>
                <div class="name">砍价</div>
                <div class="code-name">&amp;#xe60e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe666;</span>
                <div class="name">报名表单</div>
                <div class="code-name">&amp;#xe666;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64d;</span>
                <div class="name">知识专栏</div>
                <div class="code-name">&amp;#xe64d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62c;</span>
                <div class="name">商品搜索</div>
                <div class="code-name">&amp;#xe62c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe708;</span>
                <div class="name">Component-辅助分割</div>
                <div class="code-name">&amp;#xe708;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64a;</span>
                <div class="name">限时折扣</div>
                <div class="code-name">&amp;#xe64a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe614;</span>
                <div class="name">公告</div>
                <div class="code-name">&amp;#xe614;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60f;</span>
                <div class="name">个性化推荐</div>
                <div class="code-name">&amp;#xe60f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe610;</span>
                <div class="name">文章模块</div>
                <div class="code-name">&amp;#xe610;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe613;</span>
                <div class="name">知识付费会员</div>
                <div class="code-name">&amp;#xe613;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe616;</span>
                <div class="name">知识专栏</div>
                <div class="code-name">&amp;#xe616;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe617;</span>
                <div class="name">打卡</div>
                <div class="code-name">&amp;#xe617;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe677;</span>
                <div class="name">魔方</div>
                <div class="code-name">&amp;#xe677;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe642;</span>
                <div class="name">进入店铺</div>
                <div class="code-name">&amp;#xe642;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe606;</span>
                <div class="name">轮播图</div>
                <div class="code-name">&amp;#xe606;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe608;</span>
                <div class="name">大图横向滑动</div>
                <div class="code-name">&amp;#xe608;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe609;</span>
                <div class="name">小图横向滑动</div>
                <div class="code-name">&amp;#xe609;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60b;</span>
                <div class="name">导航横向滑动</div>
                <div class="code-name">&amp;#xe60b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60d;</span>
                <div class="name">一行一个</div>
                <div class="code-name">&amp;#xe60d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61b;</span>
                <div class="name">icon_图片_轮播海报</div>
                <div class="code-name">&amp;#xe61b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe69b;</span>
                <div class="name">三角形左</div>
                <div class="code-name">&amp;#xe69b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe600;</span>
                <div class="name">居右</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe603;</span>
                <div class="name">居中</div>
                <div class="code-name">&amp;#xe603;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe601;</span>
                <div class="name">居左</div>
                <div class="code-name">&amp;#xe601;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60c;</span>
                <div class="name">网页</div>
                <div class="code-name">&amp;#xe60c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60a;</span>
                <div class="name">组件</div>
                <div class="code-name">&amp;#xe60a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe741;</span>
                <div class="name">商品</div>
                <div class="code-name">&amp;#xe741;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe611;</span>
                <div class="name">图片广告</div>
                <div class="code-name">&amp;#xe611;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6c0;</span>
                <div class="name">icon_图片导航</div>
                <div class="code-name">&amp;#xe6c0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe707;</span>
                <div class="name">Component-标题文字</div>
                <div class="code-name">&amp;#xe707;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe605;</span>
                <div class="name">装修</div>
                <div class="code-name">&amp;#xe605;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>兼容性最好，支持 IE6+，及所有现代浏览器。</li>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>但是因为是字体，所以不支持多色。只能使用平台里单色的图标，就算项目里有多色图标也会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持多色图标，这些多色图标在 Unicode 模式下将不能使用，如果有需求建议使用symbol 的引用方式</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.eot');
  src: url('iconfont.eot?#iefix') format('embedded-opentype'),
      url('iconfont.woff2') format('woff2'),
      url('iconfont.woff') format('woff'),
      url('iconfont.ttf') format('truetype'),
      url('iconfont.svg#iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-sanlan"></span>
            <div class="name">
              三栏
            </div>
            <div class="code-name">.icon-sanlan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shang"></span>
            <div class="name">
              上
            </div>
            <div class="code-name">.icon-shang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiantou"></span>
            <div class="name">
              下
            </div>
            <div class="code-name">.icon-jiantou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-sousuokuang"></span>
            <div class="name">
              搜索框
            </div>
            <div class="code-name">.icon-sousuokuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wangye1"></span>
            <div class="name">
              网页
            </div>
            <div class="code-name">.icon-wangye1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-sousuokuang1"></span>
            <div class="name">
              搜索框
            </div>
            <div class="code-name">.icon-sousuokuang1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhiding"></span>
            <div class="name">
              置顶
            </div>
            <div class="code-name">.icon-zhiding
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dianxian--"></span>
            <div class="name">
              点线-1-01
            </div>
            <div class="code-name">.icon-dianxian--
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xuxian"></span>
            <div class="name">
              虚线
            </div>
            <div class="code-name">.icon-xuxian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_fengexian_shixian"></span>
            <div class="name">
              icon_分割线_实线
            </div>
            <div class="code-name">.icon-icon_fengexian_shixian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_wubianju"></span>
            <div class="name">
              icon_无边距
            </div>
            <div class="code-name">.icon-icon_wubianju
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_zuoyoubianju"></span>
            <div class="name">
              icon_左右边距
            </div>
            <div class="code-name">.icon-icon_zuoyoubianju
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fuzhuxiantiao"></span>
            <div class="name">
              辅助线条
            </div>
            <div class="code-name">.icon-fuzhuxiantiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fuzhukongbai_weixuanzhong"></span>
            <div class="name">
              辅助空白_未选中
            </div>
            <div class="code-name">.icon-fuzhukongbai_weixuanzhong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yizuosanyou"></span>
            <div class="name">
              一左三右
            </div>
            <div class="code-name">.icon-yizuosanyou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-erzuoeryou"></span>
            <div class="name">
              二左二右
            </div>
            <div class="code-name">.icon-erzuoeryou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yihangerge"></span>
            <div class="name">
              一行二个
            </div>
            <div class="code-name">.icon-yihangerge
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yihangsige"></span>
            <div class="name">
              一行四个
            </div>
            <div class="code-name">.icon-yihangsige
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yizuoeryou"></span>
            <div class="name">
              一左二右
            </div>
            <div class="code-name">.icon-yizuoeryou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yishangerxia"></span>
            <div class="name">
              一上二下
            </div>
            <div class="code-name">.icon-yishangerxia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-guding"></span>
            <div class="name">
              固定
            </div>
            <div class="code-name">.icon-guding
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-hengxianghuadong"></span>
            <div class="name">
              横向滑动
            </div>
            <div class="code-name">.icon-hengxianghuadong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-liebiaoqiehuan-"></span>
            <div class="name">
              列表切换-01
            </div>
            <div class="code-name">.icon-liebiaoqiehuan-
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tupian"></span>
            <div class="name">
              图片
            </div>
            <div class="code-name">.icon-tupian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-commodity-yihangliangge"></span>
            <div class="name">
              commodity-一行两个
            </div>
            <div class="code-name">.icon-commodity-yihangliangge
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-commodity-xiangxiliebiao"></span>
            <div class="name">
              commodity-详细列表
            </div>
            <div class="code-name">.icon-commodity-xiangxiliebiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-datumoshi"></span>
            <div class="name">
              大图模式
            </div>
            <div class="code-name">.icon-datumoshi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yihangsange"></span>
            <div class="name">
              一行三个
            </div>
            <div class="code-name">.icon-yihangsange
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_shangpintu_yidaliangxiao"></span>
            <div class="name">
              icon_商品图_一大两小
            </div>
            <div class="code-name">.icon-icon_shangpintu_yidaliangxiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xuanzemokuai-daohanghengxianghuadong"></span>
            <div class="name">
              选择模块-导航横向滑动
            </div>
            <div class="code-name">.icon-xuanzemokuai-daohanghengxianghuadong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yidaliangxiao"></span>
            <div class="name">
              一大两小
            </div>
            <div class="code-name">.icon-yidaliangxiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fuwenben"></span>
            <div class="name">
              富文本
            </div>
            <div class="code-name">.icon-fuwenben
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yuyin"></span>
            <div class="name">
              语音
            </div>
            <div class="code-name">.icon-yuyin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jifenzhang"></span>
            <div class="name">
              积分
            </div>
            <div class="code-name">.icon-jifenzhang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yunying"></span>
            <div class="name">
              运营
            </div>
            <div class="code-name">.icon-yunying
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shipin"></span>
            <div class="name">
              视频
            </div>
            <div class="code-name">.icon-shipin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zidingyimokuai"></span>
            <div class="name">
              自定义模块
            </div>
            <div class="code-name">.icon-zidingyimokuai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-laoshi"></span>
            <div class="name">
              老师
            </div>
            <div class="code-name">.icon-laoshi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-youhuiquan"></span>
            <div class="name">
              优惠券
            </div>
            <div class="code-name">.icon-youhuiquan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-kuaisuzhangfen"></span>
            <div class="name">
              快速涨粉
            </div>
            <div class="code-name">.icon-kuaisuzhangfen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-pintuan-copy"></span>
            <div class="name">
              拼团
            </div>
            <div class="code-name">.icon-pintuan-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-miaosha"></span>
            <div class="name">
              秒杀
            </div>
            <div class="code-name">.icon-miaosha
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhibobofangshexiangjitianxianxianxing"></span>
            <div class="name">
              直播 播放 摄像机 天线 线性
            </div>
            <div class="code-name">.icon-zhibobofangshexiangjitianxianxianxing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhouqi"></span>
            <div class="name">
              周期购
            </div>
            <div class="code-name">.icon-zhouqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-kecheng"></span>
            <div class="name">
              课程
            </div>
            <div class="code-name">.icon-kecheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dianpuxinxi"></span>
            <div class="name">
              店铺信息
            </div>
            <div class="code-name">.icon-dianpuxinxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-weibiaoti-_huaban"></span>
            <div class="name">
              在线客服
            </div>
            <div class="code-name">.icon-weibiaoti-_huaban
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-kanjia"></span>
            <div class="name">
              砍价
            </div>
            <div class="code-name">.icon-kanjia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-baomingbiaodan"></span>
            <div class="name">
              报名表单
            </div>
            <div class="code-name">.icon-baomingbiaodan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhishizhuanlan"></span>
            <div class="name">
              知识专栏
            </div>
            <div class="code-name">.icon-zhishizhuanlan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shangpinsousuo"></span>
            <div class="name">
              商品搜索
            </div>
            <div class="code-name">.icon-shangpinsousuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Component-fuzhufenge"></span>
            <div class="name">
              Component-辅助分割
            </div>
            <div class="code-name">.icon-Component-fuzhufenge
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xianshizhekou"></span>
            <div class="name">
              限时折扣
            </div>
            <div class="code-name">.icon-xianshizhekou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gonggao"></span>
            <div class="name">
              公告
            </div>
            <div class="code-name">.icon-gonggao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gexinghuatuijian"></span>
            <div class="name">
              个性化推荐
            </div>
            <div class="code-name">.icon-gexinghuatuijian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dianpubijikapian"></span>
            <div class="name">
              文章模块
            </div>
            <div class="code-name">.icon-dianpubijikapian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhishifufeihuiyuan"></span>
            <div class="name">
              知识付费会员
            </div>
            <div class="code-name">.icon-zhishifufeihuiyuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhishizhuanlan1"></span>
            <div class="name">
              知识专栏
            </div>
            <div class="code-name">.icon-zhishizhuanlan1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-daka"></span>
            <div class="name">
              打卡
            </div>
            <div class="code-name">.icon-daka
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-mofang"></span>
            <div class="name">
              魔方
            </div>
            <div class="code-name">.icon-mofang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jinrudianpu"></span>
            <div class="name">
              进入店铺
            </div>
            <div class="code-name">.icon-jinrudianpu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-lunbotu"></span>
            <div class="name">
              轮播图
            </div>
            <div class="code-name">.icon-lunbotu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-datuhengxianghuadong"></span>
            <div class="name">
              大图横向滑动
            </div>
            <div class="code-name">.icon-datuhengxianghuadong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiaotuhengxianghuadong"></span>
            <div class="name">
              小图横向滑动
            </div>
            <div class="code-name">.icon-xiaotuhengxianghuadong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-daohanghengxianghuadong"></span>
            <div class="name">
              导航横向滑动
            </div>
            <div class="code-name">.icon-daohanghengxianghuadong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yihangyige"></span>
            <div class="name">
              一行一个
            </div>
            <div class="code-name">.icon-yihangyige
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_tupian_lunbohaibao"></span>
            <div class="name">
              icon_图片_轮播海报
            </div>
            <div class="code-name">.icon-icon_tupian_lunbohaibao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-sanjiaoxingzuo"></span>
            <div class="name">
              三角形左
            </div>
            <div class="code-name">.icon-sanjiaoxingzuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-juyou"></span>
            <div class="name">
              居右
            </div>
            <div class="code-name">.icon-juyou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-juzhong"></span>
            <div class="name">
              居中
            </div>
            <div class="code-name">.icon-juzhong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-horizontal-left"></span>
            <div class="name">
              居左
            </div>
            <div class="code-name">.icon-horizontal-left
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wangye"></span>
            <div class="name">
              网页
            </div>
            <div class="code-name">.icon-wangye
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zujian"></span>
            <div class="name">
              组件
            </div>
            <div class="code-name">.icon-zujian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-goods"></span>
            <div class="name">
              商品
            </div>
            <div class="code-name">.icon-goods
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tupianguanggao"></span>
            <div class="name">
              图片广告
            </div>
            <div class="code-name">.icon-tupianguanggao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_tupiandaohang"></span>
            <div class="name">
              icon_图片导航
            </div>
            <div class="code-name">.icon-icon_tupiandaohang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Component-biaotiwenzi"></span>
            <div class="name">
              Component-标题文字
            </div>
            <div class="code-name">.icon-Component-biaotiwenzi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhuangxiu"></span>
            <div class="name">
              装修
            </div>
            <div class="code-name">.icon-zhuangxiu
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>兼容性良好，支持 IE8+，及所有现代浏览器。</li>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
          <li>不过因为本质上还是使用的字体，所以多色图标还是不支持的。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-sanlan"></use>
                </svg>
                <div class="name">三栏</div>
                <div class="code-name">#icon-sanlan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shang"></use>
                </svg>
                <div class="name">上</div>
                <div class="code-name">#icon-shang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiantou"></use>
                </svg>
                <div class="name">下</div>
                <div class="code-name">#icon-jiantou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-sousuokuang"></use>
                </svg>
                <div class="name">搜索框</div>
                <div class="code-name">#icon-sousuokuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wangye1"></use>
                </svg>
                <div class="name">网页</div>
                <div class="code-name">#icon-wangye1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-sousuokuang1"></use>
                </svg>
                <div class="name">搜索框</div>
                <div class="code-name">#icon-sousuokuang1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhiding"></use>
                </svg>
                <div class="name">置顶</div>
                <div class="code-name">#icon-zhiding</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dianxian--"></use>
                </svg>
                <div class="name">点线-1-01</div>
                <div class="code-name">#icon-dianxian--</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xuxian"></use>
                </svg>
                <div class="name">虚线</div>
                <div class="code-name">#icon-xuxian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_fengexian_shixian"></use>
                </svg>
                <div class="name">icon_分割线_实线</div>
                <div class="code-name">#icon-icon_fengexian_shixian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_wubianju"></use>
                </svg>
                <div class="name">icon_无边距</div>
                <div class="code-name">#icon-icon_wubianju</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_zuoyoubianju"></use>
                </svg>
                <div class="name">icon_左右边距</div>
                <div class="code-name">#icon-icon_zuoyoubianju</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fuzhuxiantiao"></use>
                </svg>
                <div class="name">辅助线条</div>
                <div class="code-name">#icon-fuzhuxiantiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fuzhukongbai_weixuanzhong"></use>
                </svg>
                <div class="name">辅助空白_未选中</div>
                <div class="code-name">#icon-fuzhukongbai_weixuanzhong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yizuosanyou"></use>
                </svg>
                <div class="name">一左三右</div>
                <div class="code-name">#icon-yizuosanyou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-erzuoeryou"></use>
                </svg>
                <div class="name">二左二右</div>
                <div class="code-name">#icon-erzuoeryou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yihangerge"></use>
                </svg>
                <div class="name">一行二个</div>
                <div class="code-name">#icon-yihangerge</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yihangsige"></use>
                </svg>
                <div class="name">一行四个</div>
                <div class="code-name">#icon-yihangsige</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yizuoeryou"></use>
                </svg>
                <div class="name">一左二右</div>
                <div class="code-name">#icon-yizuoeryou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yishangerxia"></use>
                </svg>
                <div class="name">一上二下</div>
                <div class="code-name">#icon-yishangerxia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-guding"></use>
                </svg>
                <div class="name">固定</div>
                <div class="code-name">#icon-guding</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-hengxianghuadong"></use>
                </svg>
                <div class="name">横向滑动</div>
                <div class="code-name">#icon-hengxianghuadong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-liebiaoqiehuan-"></use>
                </svg>
                <div class="name">列表切换-01</div>
                <div class="code-name">#icon-liebiaoqiehuan-</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tupian"></use>
                </svg>
                <div class="name">图片</div>
                <div class="code-name">#icon-tupian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-commodity-yihangliangge"></use>
                </svg>
                <div class="name">commodity-一行两个</div>
                <div class="code-name">#icon-commodity-yihangliangge</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-commodity-xiangxiliebiao"></use>
                </svg>
                <div class="name">commodity-详细列表</div>
                <div class="code-name">#icon-commodity-xiangxiliebiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-datumoshi"></use>
                </svg>
                <div class="name">大图模式</div>
                <div class="code-name">#icon-datumoshi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yihangsange"></use>
                </svg>
                <div class="name">一行三个</div>
                <div class="code-name">#icon-yihangsange</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_shangpintu_yidaliangxiao"></use>
                </svg>
                <div class="name">icon_商品图_一大两小</div>
                <div class="code-name">#icon-icon_shangpintu_yidaliangxiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xuanzemokuai-daohanghengxianghuadong"></use>
                </svg>
                <div class="name">选择模块-导航横向滑动</div>
                <div class="code-name">#icon-xuanzemokuai-daohanghengxianghuadong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yidaliangxiao"></use>
                </svg>
                <div class="name">一大两小</div>
                <div class="code-name">#icon-yidaliangxiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fuwenben"></use>
                </svg>
                <div class="name">富文本</div>
                <div class="code-name">#icon-fuwenben</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yuyin"></use>
                </svg>
                <div class="name">语音</div>
                <div class="code-name">#icon-yuyin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jifenzhang"></use>
                </svg>
                <div class="name">积分</div>
                <div class="code-name">#icon-jifenzhang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yunying"></use>
                </svg>
                <div class="name">运营</div>
                <div class="code-name">#icon-yunying</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shipin"></use>
                </svg>
                <div class="name">视频</div>
                <div class="code-name">#icon-shipin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zidingyimokuai"></use>
                </svg>
                <div class="name">自定义模块</div>
                <div class="code-name">#icon-zidingyimokuai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-laoshi"></use>
                </svg>
                <div class="name">老师</div>
                <div class="code-name">#icon-laoshi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-youhuiquan"></use>
                </svg>
                <div class="name">优惠券</div>
                <div class="code-name">#icon-youhuiquan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kuaisuzhangfen"></use>
                </svg>
                <div class="name">快速涨粉</div>
                <div class="code-name">#icon-kuaisuzhangfen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-pintuan-copy"></use>
                </svg>
                <div class="name">拼团</div>
                <div class="code-name">#icon-pintuan-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-miaosha"></use>
                </svg>
                <div class="name">秒杀</div>
                <div class="code-name">#icon-miaosha</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhibobofangshexiangjitianxianxianxing"></use>
                </svg>
                <div class="name">直播 播放 摄像机 天线 线性</div>
                <div class="code-name">#icon-zhibobofangshexiangjitianxianxianxing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhouqi"></use>
                </svg>
                <div class="name">周期购</div>
                <div class="code-name">#icon-zhouqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kecheng"></use>
                </svg>
                <div class="name">课程</div>
                <div class="code-name">#icon-kecheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dianpuxinxi"></use>
                </svg>
                <div class="name">店铺信息</div>
                <div class="code-name">#icon-dianpuxinxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-weibiaoti-_huaban"></use>
                </svg>
                <div class="name">在线客服</div>
                <div class="code-name">#icon-weibiaoti-_huaban</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kanjia"></use>
                </svg>
                <div class="name">砍价</div>
                <div class="code-name">#icon-kanjia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-baomingbiaodan"></use>
                </svg>
                <div class="name">报名表单</div>
                <div class="code-name">#icon-baomingbiaodan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhishizhuanlan"></use>
                </svg>
                <div class="name">知识专栏</div>
                <div class="code-name">#icon-zhishizhuanlan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shangpinsousuo"></use>
                </svg>
                <div class="name">商品搜索</div>
                <div class="code-name">#icon-shangpinsousuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Component-fuzhufenge"></use>
                </svg>
                <div class="name">Component-辅助分割</div>
                <div class="code-name">#icon-Component-fuzhufenge</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xianshizhekou"></use>
                </svg>
                <div class="name">限时折扣</div>
                <div class="code-name">#icon-xianshizhekou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gonggao"></use>
                </svg>
                <div class="name">公告</div>
                <div class="code-name">#icon-gonggao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gexinghuatuijian"></use>
                </svg>
                <div class="name">个性化推荐</div>
                <div class="code-name">#icon-gexinghuatuijian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dianpubijikapian"></use>
                </svg>
                <div class="name">文章模块</div>
                <div class="code-name">#icon-dianpubijikapian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhishifufeihuiyuan"></use>
                </svg>
                <div class="name">知识付费会员</div>
                <div class="code-name">#icon-zhishifufeihuiyuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhishizhuanlan1"></use>
                </svg>
                <div class="name">知识专栏</div>
                <div class="code-name">#icon-zhishizhuanlan1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-daka"></use>
                </svg>
                <div class="name">打卡</div>
                <div class="code-name">#icon-daka</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-mofang"></use>
                </svg>
                <div class="name">魔方</div>
                <div class="code-name">#icon-mofang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jinrudianpu"></use>
                </svg>
                <div class="name">进入店铺</div>
                <div class="code-name">#icon-jinrudianpu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lunbotu"></use>
                </svg>
                <div class="name">轮播图</div>
                <div class="code-name">#icon-lunbotu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-datuhengxianghuadong"></use>
                </svg>
                <div class="name">大图横向滑动</div>
                <div class="code-name">#icon-datuhengxianghuadong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiaotuhengxianghuadong"></use>
                </svg>
                <div class="name">小图横向滑动</div>
                <div class="code-name">#icon-xiaotuhengxianghuadong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-daohanghengxianghuadong"></use>
                </svg>
                <div class="name">导航横向滑动</div>
                <div class="code-name">#icon-daohanghengxianghuadong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yihangyige"></use>
                </svg>
                <div class="name">一行一个</div>
                <div class="code-name">#icon-yihangyige</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_tupian_lunbohaibao"></use>
                </svg>
                <div class="name">icon_图片_轮播海报</div>
                <div class="code-name">#icon-icon_tupian_lunbohaibao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-sanjiaoxingzuo"></use>
                </svg>
                <div class="name">三角形左</div>
                <div class="code-name">#icon-sanjiaoxingzuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-juyou"></use>
                </svg>
                <div class="name">居右</div>
                <div class="code-name">#icon-juyou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-juzhong"></use>
                </svg>
                <div class="name">居中</div>
                <div class="code-name">#icon-juzhong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-horizontal-left"></use>
                </svg>
                <div class="name">居左</div>
                <div class="code-name">#icon-horizontal-left</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wangye"></use>
                </svg>
                <div class="name">网页</div>
                <div class="code-name">#icon-wangye</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zujian"></use>
                </svg>
                <div class="name">组件</div>
                <div class="code-name">#icon-zujian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-goods"></use>
                </svg>
                <div class="name">商品</div>
                <div class="code-name">#icon-goods</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tupianguanggao"></use>
                </svg>
                <div class="name">图片广告</div>
                <div class="code-name">#icon-tupianguanggao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_tupiandaohang"></use>
                </svg>
                <div class="name">icon_图片导航</div>
                <div class="code-name">#icon-icon_tupiandaohang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Component-biaotiwenzi"></use>
                </svg>
                <div class="name">Component-标题文字</div>
                <div class="code-name">#icon-Component-biaotiwenzi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhuangxiu"></use>
                </svg>
                <div class="name">装修</div>
                <div class="code-name">#icon-zhuangxiu</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
