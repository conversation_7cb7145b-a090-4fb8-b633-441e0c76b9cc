<template>
  <el-upload
    class="upload"
    action="/scenic/api-v2/aws/uploadFile"
    :show-file-list="false"
    :data="{ type: 'image' }"
    :on-success="handleAvatarSuccess"
    :on-error="handleAvatarError"
    :before-upload="beforeAvatarUpload"
  >
    <div v-if="imageUrl" class="avatar"><img :src="imageUrl" /></div>
    <span v-else class="icon"
      ><el-icon><Plus /></el-icon
    ></span>
  </el-upload>
</template>

<script setup>
import { Plus } from '@element-plus/icons-vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { ref, watch } from 'vue'

// eslint-disable-next-line no-undef
const props = defineProps(['modelValue'])
// eslint-disable-next-line no-undef
const emits = defineEmits(['update:modelValue'])
let imageUrl = ref()
const handleAvatarSuccess = (res) => {
  emits('update:modelValue', process.env.VUE_APP_IMG_URL + res.data.path)
}
const handleAvatarError = (res) => {
  // 弹窗提示
  ElMessage.error('上传失败')
}
const beforeAvatarUpload = (file) => {
  // 修改文件名
  const newFileName = 'new_name.' + file.name.split('.').pop()
  const newFile = new File([file], newFileName, { type: file.type })
  console.log(newFile)
    // 判断是否为localhost本地开发环境
  const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
  if (isLocalhost) {
    // 开发时辅助上传图片，解决环境问题无法上传图片的问题
    emits('update:modelValue', "https://prod.shukeyun.com/maintenance/deepfile/data/2025-08-13/upload_ee382a1c76fc3dcbcf424b0f297b1024.png")
  }
  return newFile
}
watch(
  props,
  () => {
    imageUrl.value = props.modelValue
  },
  { immediate: true }
)
</script>

<style scoped lang="less">
.upload {
  display: flex;
  .avatar,
  .icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    box-sizing: border-box;
    overflow: hidden;
  }
  .avatar {
    border: 1px solid #dcdfe6;
    position: relative;
    > img {
      width: 100%;
      height: 100%;
    }
    &::after {
      content: '更换';
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 20px;
      background: #000;
      text-align: center;
      line-height: 20px;
      font-size: 12px;
      color: #fff;
      opacity: 0;
      transition: 0.5s;
    }
  }
  .icon {
    border: 1px dashed #dcdfe6;
    transform: 0.2s;
    font-size: 28px;
    color: #8c939d;
    cursor: pointer;
    transition: 0.5s;
  }
  &:hover {
    .avatar::after {
      opacity: 0.63;
    }
    .icon {
      border-color: #337ecc;
    }
  }
}
</style>
