<template>
  <div id="app">
    <div v-if="logining">登录中...</div>
    <router-view v-if="!logining && isRouterAlive" />
  </div>
</template>

<script setup>
import api from '@/api/api';
import { nextTick, provide, ref } from 'vue';
let isRouterAlive = ref(true)

console.log('app.vue')

// 获取路由参数
function getUrlParameter(name = '') {
  // 创建一个空对象来存储参数
  const params = {}

  // 使用 window.location.search 获取查询字符串
  // 使用 decodeURIComponent 来解码，然后去除 '?' 符号
  const queryString = window.location.hash.split('?')[1]

  // 将查询字符串分割成键值对
  const vars = queryString.split('&')

  // 遍历键值对
  for (let i = 0; i < vars.length; i++) {
    const pair = vars[i].split('=')

    // 解码并存储参数
    params[pair[0]] = decodeURIComponent(pair[1])
  }

  // 如果指定了参数名称，则返回该参数的值，否则返回所有参数的对象
  return name ? params[name] : params
}
const logining = ref(true)
const { tk, _local } = getUrlParameter()
console.log(tk, _local)
if (_local) {
  // 跳转到本地
  window.location.replace(_local+(tk?`&tk=${tk}`:''))
} else if (tk) {
  api
    .setCookie(tk)
    .then((res) => {
      history.pushState(null, null, location.href.substring(0,location.href.indexOf('&tk=')))
      logining.value = false
    })
    .catch((err) => {
      console.log('catch')
      console.log(err)
    })
} else {
  logining.value = false
}

// reload 无感刷新事件
const reload = () => {
  isRouterAlive.value = false
  // nextTick：在下次 DOM 更新循环结束之后执行延迟回调。在修改数据之后立即使用这个方法，获取更新后的 DOM。
  nextTick(() => {
    isRouterAlive.value = true
  })
}

// 向子组件以及子孙组件传递名为 reload 的函数，第一个参数自定义，第二个参数代表上面定义的 reload() 方法
provide('reload', reload)
</script>

<style lang="less">
#app {
  position: absolute;
  left: 0;
  top: 0;
}

.imgText {
  flex: 1;
}

// 页面切换动画
.fade-enter-active {
  transition: all 1.5s ease;
}
.fade-leave-active {
  transition: all 1.5s ease;
}
.fade-enter {
  transform: translateX(5px);
  opacity: 0;
}
.fade-leave-to {
  transform: translateX(5px);
  opacity: 0;
}
.el-message-box.JSONView {
  width: 1100px;
}
.el-form-item__content {
  display: block !important;
}

// 提示样式
.Prompt {
  font-size: 12px;
  color: #969799;
  margin-bottom: 10px;
}

// 该元素不接受鼠标事件
.pointer-events {
  pointer-events: none;
}

.line-title {
  position: relative;
  &::before {
    content: '';
    display: block;
    width: 3px;
    height: 16px;
    background: #1890ff;
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    z-index: 1;
  }
  padding-left: 7px;
  font-weight: 600;
  font-size: 16px;
  color: #000000;
}
.line-dec {
  margin-bottom: 20px;
  font-weight: 400;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.5);
}
.van-overlay,
.van-popup {
  z-index: 9999 !important;
}
</style>
