const componentProperties = new Map()
// 图片广告
componentProperties.set('pictureads', {
  component: 'pictureads',
  text: '图片广告',
  type: '1-1',
  active: true,
  style: 'pictureadsstyle',
  setStyle: {
    text: '图片广告',
    swiperType: '1', // 选择模板 { 0: '一行一个', 1: '轮播海报', 2: '双层轮播', 3: '横向滑动' }
    autoSwitch: true, // 自动轮播 @if (swiperType == 1 || swiperType == 2)
    durationType: '0', // 切换间隔 { 0: '3 秒', 1: '4 秒', 2: '5 秒' } @if (swiperType == 1 || 2)
    indicatorType: '0', // 轮播提示 { 0: '样式一', 1: '样式二', 2: '样式三', 3: '样式四', 4: '样式五' } @if (swiperType == 1 || 2)
    screenType: '0', // 一屏展示 { 0: '1 个图片', 1: '2 个图片' } @if (swiperType == 3)
    pictureType: 'routine', // 图片样式 { routine: '常规', projection: '投影' }
    aspectType: 'fill', // 填充样式 { fill: '填充', fit: '留白' } @if (swiperType == 1 || 2)
    borderRadius: 0, // 图片圆角 [0~211/2] 211/2 为组件高度一半
    imageMargin: 0, // 图片间距 [0~30] @if (swiperType == 0 || 3)
    pageMargin: 0, // 页面边距 [0~30]
    moduleMargin: 0, // 组件间距 [-211~30] 211 为组件高度
    imageList: [
      // 图片集合
      {
        text: '', // 图片标题
        src: '', // 图片链接
        srcBg: '', // 图片背景
        link: '', // 跳转链接
        linkType: '1', // 跳转类型 { 0: '内部链接', 1: '外部链接' }
      },
    ],
  },
})
// 图文导航
componentProperties.set('graphicnavigation', {
  component: 'graphicnavigation',
  text: '图文导航',
  type: '1-2',
  active: true,
  style: 'graphicnavigationstyle',
  setStyle: {
    text: '图文导航',
    navigationType: '1', // 选择模板 { 0: '单行滑动', 1: '两行排列' }
    navigationList: [
      {
        text: '门票预订', // 图片标题
        src: 'https://minio-prod.shukeyun.com/scenic-prod/01.png', // 图片链接
        link: 'ticketList', // 跳转链接
        linkType: '0', // 跳转类型 { 0: '内部链接', 1: '外部链接' }
      },
      {
        text: '权益卡', // 图片标题
        src: 'https://minio-prod.shukeyun.com/scenic-prod/02.png', // 图片链接
        link: 'travelCardList', // 跳转链接
        linkType: '0', // 跳转类型 { 0: '内部链接', 1: '外部链接' }
      },
      {
        text: '活动资讯', // 图片标题
        src: 'https://minio-prod.shukeyun.com/scenic-prod/03.png', // 图片链接
        link: 'information', // 跳转链接
        linkType: '0', // 跳转类型 { 0: '内部链接', 1: '外部链接' }
      },
      {
        text: '攻略游记', // 图片标题
        src: 'https://minio-prod.shukeyun.com/scenic-prod/04.png', // 图片链接
        link: 'travelNotes', // 跳转链接
        linkType: '0', // 跳转类型 { 0: '内部链接', 1: '外部链接' }
      },
    ], // 导航集合
    borderRadius: 0, // 图片圆角
    textSize: 13, // 字体大小 [12~50]
    textColor: 'rgba(0,0,0,1)', // 文字颜色
    textHeight: 20, // 文字高度 [12~50]
  },
})
// 商品分组
componentProperties.set('productgroup', {
  component: 'productgroup',
  text: '商品分组',
  type: '1-3',
  active: true,
  style: 'productgroupstyle',
  setStyle: {
    text: '商品分组',
    groupType: '0', // 选择模板 { 0: '横向滑动', 1: '一行两个' }
    textType: 'routine', // 文字样式 { routine: '常规', bold: '加粗' }
    pointType: 'left', // 文字位置 { left: '左对齐', center: '居中' }
    scaleType: '0', // 图片比例 { 0: '3:2', 1: '1:1', 2: '3:4', 3: '16:9' }
    aspectType: 'fill', // 填充样式 { fill: '填充', fit: '留白' }
    borderRadius: 6, // 图片圆角
    productMargin: 8, // 商品间距 [0~30]
    pageMargin: 15, // 页面边距 [0~30]
    priceSwitch: true, // 商品价格
    textSwitch: true, // 商品名称
    groupList: [
      {
        text: '景区门票', // 分组标题
        goodsList: [], // 商品列表
      },
      {
        text: '组合套票', // 分组标题
        goodsList: [], // 商品列表
      },
      {
        text: '权益卡', // 分组标题
        goodsList: [], // 商品列表
      },
    ],
  },
})
// 文章管理
componentProperties.set('articlemanage', {
  component: 'articlemanage',
  text: '文章管理',
  type: '1-4',
  active: true,
  style: 'articlemanagestyle',
  setStyle: {
    text: '文章列表', // 组件名称
    articleType: 'flow', // 选择模板 { flow: '瀑布流', around: '左右图文', large : '大图列表' }
    sourceType: '1', // 文章类型 { 1: '攻略', 2: '资讯', 3: '活动', 4: '游记' }
    cardType: 'projection', // 卡片样式 { white: '无边白底', projection: '卡片投影', outline: '描边白底', transparent: '透明底' }
    textType: 'routine', // 文字样式 { routine: '常规', bold: '加粗' }
    moreType: '2', // 查看更多 { 0: '上方显示', 1: '下方显示', 2: '隐藏 }
    borderRadius: 6, // 图片圆角
    imageMargin: 10, // 图片间距 [0~30]
    pageMargin: 15, // 页面边距 [0~30]
    authorSwitch: true, // 作者 @if (articleType == flow || articleType == around)
    viewsSwitch: true, // 阅读数 @if (articleType == flow || articleType == around)
  },
})
// 辅助分割
componentProperties.set('segmentation', {
  component: 'segmentation',
  text: '辅助分割',
  type: '1-5',
  active: true,
  style: 'segmetnationstyle',
  setStyle: {
    text: '辅助分割',
    type: 'blank', // 分割类型 blank：辅助空白  line：辅助线
    blankHeight: 20, // 空白高度 [0~100]
    pattern: 'solid', // 线的样式 { 0: '实线', 1: '虚线', 2：‘点线’ } @if (type == line)
    pageMargin: 0, // 页面边距 [0~30]
    color: '#D9D9D9', // 背景颜色
  },
})
// 图片魔方
componentProperties.set('magiccube', {
  component: 'magiccube',
  text: '图片魔方',
  type: '1-6',
  active: true,
  style: 'magiccubestyle',
  setStyle: {
    text: '图片魔方',
    rubiksCubeType: 0, // 魔方类型
    rubiksStyle: {
      type: 0,
      height: 187.5,
      list: [
        {
          top: 0,
          left: 0,
          width: 187.5,
          height: 187.5,
          src: '',
        },
        {
          top: 0,
          left: 187.5,
          width: 187.5,
          height: 187.5,
          src: '',
        },
      ],
    }, // 魔方样式
    pageMargin: 0, //页面边距
    imgMargin: 0, //图片间距
    borderRadius: 0, //图片圆角
    imageList: [
      {
        src: '',
        linktype: '10',
        http: {},
      },
      {
        src: '',
        linktype: '10',
        http: {},
      },
      {
        src: '',
        linktype: '10',
        http: {},
      },
      {
        src: '',
        linktype: '10',
        http: {},
      },
      {
        src: '',
        linktype: '10',
        http: {},
      },
    ], //图片列表
  },
})
// 标题文本
componentProperties.set('titletext', {
  component: 'titletext',
  text: '标题文本',
  type: '1-6',
  active: true,
  style: 'titletextstyle',
  setStyle: {
    title: '',
    description: '',
    position: 'left', // 显示位置 { left: '左对齐', center: '居中' }
    titleSize: '16', // 标题大小 [12~50]
    desSize: '12', // 描述大小 [12~50]
    titleWeight: 'bold', // 标题粗细 { routine: '常规', bold: '加粗' }
    desWeight: 'routine', // 描述粗细 { routine: '常规', bold: '加粗' }
    titleColor: '#323233', // 标题颜色
    desColor: '#969799', // 描述颜色
    pageMargin: 15, // 页面边距 [0~30]
    bgColor: '#fff', // 背景颜色
    partingLine: 0, // 底部分割线 { 0: '不显示', 1: '显示' }
    lookMore: 0, // 查看更多 { 0: '不显示', 1: '显示' }
    lookMoreType: 0, // 查看更多样式 { left: '左对齐', center: '居中' }
    lookMoreText: '查看更多', // 查看更多文字
  },
})
// 全局搜索
componentProperties.set('globalsearch', {
  component: 'globalsearch',
  text: '全局搜索',
  type: '1-1',
  active: true,
  style: 'globalsearchstyle',
  setStyle: {
    text: '全局搜索',
    swiperType: '1', // 选择模板 { 0: '一行一个', 1: '轮播海报', 2: '双层轮播', 3: '横向滑动' }
    autoSwitch: true, // 自动轮播 @if (swiperType == 1 || swiperType == 2)
    durationType: '0', // 切换间隔 { 0: '3 秒', 1: '4 秒', 2: '5 秒' } @if (swiperType == 1 || 2)
    indicatorType: '0', // 轮播提示 { 0: '样式一', 1: '样式二', 2: '样式三', 3: '样式四', 4: '样式五' } @if (swiperType == 1 || 2)
    screenType: '0', // 一屏展示 { 0: '1 个图片', 1: '2 个图片' } @if (swiperType == 3)
    pictureType: 'routine', // 图片样式 { routine: '常规', projection: '投影' }
    aspectType: 'fill', // 填充样式 { fill: '填充', fit: '留白' } @if (swiperType == 1 || 2)
    borderRadius: 0, // 图片圆角 [0~211/2] 211/2 为组件高度一半
    imageMargin: 0, // 图片间距 [0~30] @if (swiperType == 0 || 3)
    pageMargin: 0, // 页面边距 [0~30]
    moduleMargin: 0, // 组件间距 [-211~30] 211 为组件高度
    searchHeight: 30,

    bgColor: '#fff', // 背景颜色
    borderColor: '#fff', // 边框颜色
    textColor: '#999', // 文字颜色
    textAlign: 'left', // 文字位置 { left: '左对齐', center: '居中' }
    borderType: 'quadrate', // 边框样式 { quadrate: '方形', circular: '圆形' }
    positionType: 'initial', // 显示位置 { normal: '常规', top: '顶部' }
    hotWordType: 'auto', // 热词类型 { auto: '自动',  manual: '手动' }
    hotWordList: [
      // 热词列表
      {
        text: '111',
      },
    ],
  },
})
// 门票设计 - 文本区域
componentProperties.set('textblock', {
  component: 'textblock',
  text: '文本区域',
  type: '1-1',
  active: false,
  style: 'textblockstyle',
  setStyle: {
    width: 60, // 宽度
    height: 100, // 高度
    left: 0, // 左边距
    top: 0, // 上边距
    columnNum: '1', // 列数
    alignType: 'left', // 对齐方式
    lineSpacing: 5, // 行间距
    fields: [],
  },
})
// 门票设计 - 二维码
componentProperties.set('ticketcode', {
  component: 'ticketcode',
  text: '二维码',
  type: '1-2',
  active: false,
  style: 'ticketcodestyle',
  setStyle: {
    width: 30, // 宽度
    height: 30, // 高度
    left: 0, // 左边距
    top: 0, // 上边距
    columnNum: '1', // 列数
    alignType: 'left', // 对齐方式
    lineSpacing: 10, // 行间距
    fields: [],
  },
})

componentProperties.set('topimg', {
  component: 'topimg',
  text: '顶部图片',
  type: '1-9',
  active: true,
  style: 'topimgstyle',
  setStyle: {
    text: '顶部图片',
    isShowQuestion: true, // 问一问组件是否展示
    imgUrl: '',
  },
})

// 商品工坊
componentProperties.set('productWorkshop', {
  component: 'productWorkshop',
  text: '商品工坊',
  type: '2-1',
  active: true,
  style: 'productWorkshopstyle',
  setStyle: {
    text: '商品工坊',
  },
})

// 智能体
componentProperties.set('agent', {
  component: 'agent',
  text: '智能体',
  type: '3-1',
  active: true,
  style: 'agentstyle',
  setStyle: {
    text: '智能体',
    compName: '导览智能体',
    agentList: [],
    agentSpacing: 10,
    pageMargin: 15,
    visitorsShow: true,
    swiperType: 1,
  },
})

// 攻略定制
componentProperties.set('destinationSelection', {
  component: 'destinationSelection',
  text: '目的地精选',
  type: '2-2',
  active: true,
  style: 'destinationSelectionStyle',
  setStyle: {
    text: '目的地精选',
  },
})

// 攻略定制
componentProperties.set('strategyCustomization', {
  component: 'strategyCustomization',
  text: '攻略定制',
  type: '3-2',
  active: true,
  style: 'strategyCustomizationStyle',
  setStyle: {
    text: '攻略定制',
  },
})

// 数据版本  每次修改组件数据  需要对版本进行修改
// componentProperties.set('componentPropertiesVersion', 'V1.0.0')
/*
componentProperties.set('captiontext', {
  component: 'captiontext',
  text: '标题文字',
  type: '1-3',
  active: true,
  style: 'captiontextsstyle',
  setStyle: {
    text: '标题文字',
    name: '标题文字', //标题内容
    description: '', //描述内容
    wordSize: 16, //标题大小
    descriptionSize: 12, //描述大小
    wordWeight: 400, //标题粗细
    positions: 'left', //显示位置  可选 left/center
    descriptionWeight: 200, //描述粗细
    wordColor: 'rgba(50, 50, 51, 10)', //标题颜色
    descriptionColor: 'rgba(150, 151, 153, 10)', //描述颜色
    backColor: 'rgba(255, 255, 255, 10)', //背景颜色
    borderBott: false, //底部分割线
    wordHeight: 24, //框体高度
    more: {
      //查看更多
      show: false, //是否显示查看更多
      type: 1, // 样式选择
      text: '查看更多', //自定义文字
      httpType: 10, //链接类型
      http: '', //链接
    },
  },
})
componentProperties.set('listswitching', {
  component: 'listswitching',
  text: '商品',
  type: '2-1',
  active: true,
  style: 'listswitchingstyle',
  setStyle: {
    text: '商品',
    commodityType: 0,
    moditystyle: 0,
    borderRadius: 0,
    pageMargin: 15,
    commodityMargin: 10,
    textWeight: 400,
    positions: 'left',
    priceofcommodity: true,
    purchasebutton: true,
    commoditycorner: true,
    purchasebuttonType: 0,
    commoditycornertype: 0,
    commodityTagColor: '#07c160',
    tagPosition: 0,
    imageList: [],
    purchase: '马上抢',
    commoditylisttype: 0,
    commoditylisttypetab: [
      {
        text: '分组',
        imageList: [],
      },
      {
        text: '分组',
        imageList: [],
      },
    ],
    tabColor: '#f39800',
    showMore: false,
    moreUrl: null,
    bgImg: '',
  },
})
componentProperties.set('richtext', {
  component: 'richtext',
  text: '富文本',
  type: '1-10',
  active: true,
  style: 'richtextstyle',
  setStyle: {
    text: '富文本',
    myValue: '', //富文本内容
    backColor: 'rgb(249, 249, 249)', //背景颜色
  },
})
componentProperties.set('magiccube', {
  component: 'magiccube',
  text: '魔方',
  type: '1-6',
  active: true,
  style: 'magiccubestyle',
  setStyle: {
    text: '魔方',
    rubiksCubeType: 0, // 魔方类型
    pageMargin: 0, //页面边距
    imgMargin: 0, //图片间距
    imageList: [
      {
        src: '',
        linktype: '10',
        http: {},
      },
      {
        src: '',
        linktype: '10',
        http: {},
      },
      {
        src: '',
        linktype: '10',
        http: {},
      },
      {
        src: '',
        linktype: '10',
        http: {},
      },
      {
        src: '',
        linktype: '10',
        http: {},
      },
    ], //图片列表
  },
})
componentProperties.set('auxiliarysegmentation', {
  component: 'auxiliarysegmentation',
  text: '辅助分割',
  type: '1-11',
  active: true,
  style: 'auxiliarysegmentationstyle',
  setStyle: {
    text: '辅助分割',
    blankHeight: 30, //空白高度
    segmentationtype: 0, //分割类型
    paddType: 0, //边距
    auxliarColor: 'rgb(229, 229, 229)', //辅助线颜色
    bordertp: 'solid', //线的类型
  },
})
componentProperties.set('commoditysearch', {
  component: 'commoditysearch',
  text: '商品搜索',
  type: '1-1',
  active: true,
  style: 'commoditysearchstyle',
  setStyle: {
    text: '商品搜索',
    heights: 28, //搜索栏高度
    position: 0, //显示位置
    sweep: false, // 显示扫一扫
    borderRadius: 0, //框体样式
    textPosition: 0, //文本位置
    backgroundColor: 'rgb(249, 249, 249)', //背景颜色
    borderColor: 'rgb(255, 255, 255)', //框体颜色
    textColor: 'rgb(150, 151, 153)', //字体颜色
    hotords: [], //热词
  },
})
componentProperties.set('storeinformation', {
  component: 'storeinformation',
  text: '店铺信息',
  type: '1-12',
  active: true,
  style: 'storeinformationstyle',
  setStyle: {
    text: '店铺信息',
    bakcgroundImg: '', //背景图片
    headPortrait: '', //店铺头像
    rubiksCubeType: 0, //类型
    name: '店铺名称', //店铺名称
    Discount: '在线支付满 150 减 30，满 100 减 20', //优惠信息
  },
})
componentProperties.set('entertheshop', {
  component: 'entertheshop',
  text: '单元格',
  type: '1-13',
  active: true,
  style: 'entertheshopstyle',
  setStyle: {
    text: '单元格',
    icon: '', // 左侧图标
    shopName: '左侧标题',
    copywriting: '右侧内容',
    type: '10',
    http: {},
  },
})
componentProperties.set('notice', {
  component: 'notice',
  text: '公告',
  type: '1-7',
  active: true,
  style: 'noticestyle',
  setStyle: {
    text: '公告',
    noticeText: '请填写内容，如果过长，将会在手机上滚动显示', //内容
    backColor: 'rgb(255, 248, 233)', //背景颜色
    textColor: 'rgba(100, 101, 102)', //文字颜色
  },
})
componentProperties.set('videoss', {
  component: 'videoss',
  text: '视频',
  type: '1-8',
  active: true,
  style: 'videostyle',
  setStyle: {
    text: '视频',
    src: 'http://clips.vorwaerts-gmbh.de/big_buck_bunny.mp4', // 视频地址
    coverUrl: null, // 封面地址
    autoplay: false, // 是否自动播放
  },
})
componentProperties.set('custommodule', {
  component: 'custommodule',
  text: '自定义模块',
  type: 'demo',
  active: true,
  style: 'custommodulestyle',
  setStyle: {
    text: '自定义模块',
    demo: '自定义内容',
    img: 'https://img2.baidu.com/it/u=1905875968,4289754134&fm=26&fmt=auto&gp=0.jpg',
  },
})
componentProperties.set('communitypowder', {
  component: 'communitypowder',
  text: '社群涨粉',
  type: '1-14',
  active: true,
  style: 'communitypowderstyle',
  setStyle: {
    text: '社群涨粉',
    mainImg: '', //入口图片
    qrcodeImg: '', //二维码
    title: '标题', //标题
    describe: '辅助描述', //描述
    buttonName: '立即添加', //按钮名称
    backColor: 'rgb(255, 255, 255)', //背景颜色
  },
})
componentProperties.set('storenotecard', {
  component: 'storenotecard',
  text: '文章模块',
  type: '2-2',
  active: true,
  style: 'storenotecardstyle',
  setStyle: {
    text: '文章模块',
    name: '这里显示专题名称', //专题名称
    commodityType: 0, // 选择模板
    moditystyle: 0, // 卡片样式选择
    borderRadius: 0, // 图片边角
    textWeight: 400, // 标题粗细
    noteLabels: true, // 笔记标签
    readingNumber: true, // 阅读数
    praisePoints: true, //点赞数
    viewMore1: true, //更多 1
    viewMore2: true, //更多 2
    imageList: [],
    positions: 'bottom', //标题位置
    linktype: '10',
    http: {},
  },
})
componentProperties.set('crowdoperation', {
  component: 'crowdoperation',
  text: '人群运营',
  type: '1-17',
  active: true,
  style: 'crowdoperationstyle',
  setStyle: {
    text: '人群运营',
  },
})
componentProperties.set('personalizedrecommendation', {
  component: 'personalizedrecommendation',
  text: '个性化推荐',
  type: '1-18',
  active: true,
  style: 'personalizedrecommendationstyle',
  setStyle: {
    text: '个性化推荐',
  },
})
componentProperties.set('onlineservice', {
  component: 'onlineservice',
  text: '在线客服',
  type: '1-19',
  active: true,
  style: 'onlineservicestyle',
  setStyle: {
    text: '在线客服',
  },
})
componentProperties.set('investigate', {
  component: 'investigate',
  text: '表单模块',
  type: '2-3',
  active: true,
  style: 'investigatestyle',
  setStyle: {
    text: '表单模块',
    title: '表单模块',
    jsonData: [], //value1 为 sass 显示内容，value2 为前端显示内容
  },
})
componentProperties.set('tabBar', {
  component: 'tabBar',
  text: '底部导航',
  type: '1-5',
  active: true,
  style: 'tabBarStyle',
  setStyle: {
    text: '底部导航',
    activeColor: '#1989fa',
    inactiveColor: '#7d7e80',
    isShowBorder: true,
    iconWidth: '25',
    iconHeight: '25',
    fontSize: '14',
    Highlight: 0,
    iconList: [],
  },
})
componentProperties.set('follow', {
  component: 'follow',
  text: '关注公众号',
  type: '1-15',
  active: true,
  style: 'followStyle',
  setStyle: {
    text: '关注公众号',
    heade: 'https://imgs.starfirelink.com/miniShop//logo_1618466110849.png',
    followName: '公众号名称',
    followAppId: '',
  },
})
componentProperties.set('suspension', {
  component: 'suspension',
  text: '悬浮按钮',
  type: '1-16',
  active: true,
  style: 'suspensionstyle',
  setStyle: {
    text: '悬浮按钮',
    linktype: '10',
    http: {},
  },
})
*/

export default componentProperties
