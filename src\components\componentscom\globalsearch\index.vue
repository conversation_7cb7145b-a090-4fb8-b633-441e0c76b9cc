<template>
  <div class="search-box">
    <!-- 组件 -->
    <div
      class="search-panel"
      :style="{
        height: datas.searchHeight + 'px',
      }"
    >
      <div
        class="s-input"
        :style="{
          borderRadius: datas.borderType === 'quadrate' ? '0px' : '15px',
          justifyContent: datas.textAlign === 'center' ? 'center' : 'start',
          backgroundColor: datas.bgColor,
          color: datas.textColor,
          border: `1px solid ${datas.borderColor}`,
        }"
      >
        <svgcom class="s-input-icon" name="globalsearch" />查找地点
      </div>
      <!-- <div class="s-btn">搜索</div> -->
    </div>
    <!-- 删除组件 -->
    <slot name="deles" />
  </div>
</template>

<script>
export default {
  name: 'globalsearch',
  props: {
    datas: Object,
  },
  data() {
    return {
      // swiperIndex: 0,
      // timeId: null,
      // defaultImg: require('@/assets/images/slideshow.svg'),
    }
  },
  methods: {
    // auto() {
    //   if (
    //     (this.datas.swiperType == 1 || this.datas.swiperType == 2) &&
    //     this.datas.autoSwitch
    //   ) {
    //     this.timeId = setTimeout(() => {
    //       this.swiperIndex +=
    //         this.swiperIndex < this.getImageList.length - 1
    //           ? 1
    //           : -this.swiperIndex
    //       this.auto()
    //     }, [3000, 4000, 5000][this.datas.durationType])
    //   } else {
    //     this.swiperIndex = 0
    //   }
    // },
  },
  created() {
    // this.auto()
  },
  computed: {
    // getImageList() {
    //   return this.datas.imageList.filter((item) => item.src)
    // },
  },
  // watch: {
  //   'datas.autoSwitch'() {
  //     clearTimeout(this.timeId)
  //     this.auto()
  //   },
  //   'datas.swiperType'() {
  //     clearTimeout(this.timeId)
  //     this.auto()
  //   },
  // },
}
</script>

<style scoped lang="less">
.search-box {
  position: relative;
  padding: 7px 15px;
  background-color: #fff;
  .search-panel {
    height: 30px;
    display: flex;
    align-items: center;
    .s-input {
      padding-left: 10px;
      height: 100%;
      flex: 1;
      display: flex;
      align-items: center;
      background-color: #f0f0f0;
      border-radius: 15px;
      .s-input-icon {
        margin-right: 5px;
        width: 20px;
        height: 20px;
      }
    }
    .s-btn {
      width: 40px;
      flex: none;
      text-align: right;
      color: #000;
    }
  }
}
</style>
