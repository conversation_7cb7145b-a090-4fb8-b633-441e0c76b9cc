<template>
  <div class="typecom">
    <div
      v-for="item in list"
      :key="item.value"
      :class="{ active: item.value == modelValue }"
      @click="$emit('update:modelValue', item.value)"
    >
      <svg class="svgIcon" aria-hidden="true">
        <use :xlink:href="'#' + item.icon"></use>
      </svg>
      <span>{{ item.name }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'typecom',
  props: {
    list: Array,
    modelValue: String,
  },
}
</script>

<style lang="less" scoped>
.typecom {
  display: flex;
  align-items: center;
  gap: 30px;
  > div {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    .svgIcon {
      width: 60px;
      height: 60px;
      color: #777;
      transition: all 0.5s;
    }
    > span {
      transition: all 0.5s;
    }
    &:hover,
    &.active {
      .svgIcon,
      > span {
        color: #1890ff;
      }
    }
  }
}
</style>
