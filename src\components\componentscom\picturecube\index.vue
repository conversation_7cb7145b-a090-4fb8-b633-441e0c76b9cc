<template>
  <div style="position: relative">
    图片魔方
    <!-- 删除组件 -->
    <slot name="deles" />
  </div>
</template>

<script>
export default {
  name: 'picturecube',
  props: {
    datas: Object,
  },
  data() {
    return {
      swiperIndex: 0,
      timeId: null,
      defaultImg: require('@/assets/images/slideshow.svg'),
    }
  },
  methods: {
    auto() {
      if (
        (this.datas.swiperType == 1 || this.datas.swiperType == 2) &&
        this.datas.autoSwitch
      ) {
        this.timeId = setTimeout(() => {
          this.swiperIndex +=
            this.swiperIndex < this.datas.imageList.length - 1
              ? 1
              : -this.swiperIndex
          this.auto()
        }, [3000, 4000, 5000][this.datas.durationType])
      } else {
        this.swiperIndex = 0
      }
    },
  },
  created() {
    this.auto()
  },
  watch: {
    'datas.autoSwitch'() {
      clearTimeout(this.timeId)
      this.auto()
    },
    'datas.swiperType'() {
      clearTimeout(this.timeId)
      this.auto()
    },
  },
}
</script>

<style scoped lang="less">
.pictureads {
  position: relative;
  .default {
    width: 100%;
    height: 211px;
    background: #f3f5f6;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    > img {
      width: 148px;
    }
    > span {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.5);
    }
  }
  .module {
    position: relative;
    overflow: hidden;
    .swiperBg {
      > img {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        width: 100%;
        height: 100%;
        transition: 1s;
        opacity: 0;
      }
      .active {
        opacity: 1;
      }
    }
    .swiper {
      position: relative;
      display: flex;
      transition: 0.3s;
      > img {
        flex: none;
      }
    }
  }
  .tips {
    position: absolute;
    bottom: 7.5px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 5px;
    > div {
      background: #d8d8d8;
      transition: 0.2s;
    }
    .active {
      background: #fff;
    }
  }
  .tips0 {
    > div {
      width: 8px;
      height: 8px;
      border-radius: 4px;
    }
    .active {
      width: 17px;
    }
  }
  .tips1 {
    > div {
      width: 17px;
      height: 6px;
    }
  }
  .tips2,
  .tips3 {
    position: absolute;
    right: 10px;
    bottom: 7.5px;
    width: 27px;
    height: 15px;
    text-align: center;
    line-height: 15px;
    border-radius: 15px;
    font-size: 11px;
    color: #fff;
    transition: 0.2s;
  }
  .tips3 {
    background: rgba(2, 2, 2, 0.52);
  }
}
</style>
