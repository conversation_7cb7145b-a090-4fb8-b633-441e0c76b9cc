/**
 * axios 封装
 * 请求拦截、相应拦截、错误统一处理=
 */

import axios from 'axios'
import { Toast } from 'vant'

let loadingInstance = null // 全局加载 loadding

const instance = axios.create({
  // 请求头
  headers: {
    'X-Trace-Id':
      Math.random().toString(36).substring(2, 6) + '-' + new Date().getTime(), // 本次请求唯一标识
  },
  //创建 axios 实例
  timeout: 10000, // 设置超时时间 10s
  baseURL:
    process.env.NODE_ENV == 'development'
      ? '/api'
      : process.env.VUE_APP_BASE_URL,
  // baseURL: window.global_config.BASE_URL, // 请求配置静态文件 config/config.js		dev 模式下为 api 走代理模式
})

// 设置默认请求头
instance.defaults.headers.post['Content-Type'] =
  'application/x-www-form-urlencoded'

// 跨域允许携带 cookie
instance.defaults.withCredentials = true

// 请求拦截器
instance.interceptors.request.use(
  (request) => {
    // 在发送请求前验证 token 并添加 token 请求头
    // if (getToken()) request.headers['token'] = getToken()
    loadingInstance = Toast.loading({
      message: '拼命加载中...',
      forbidClick: true,
      duration: 0,
      overlay: true,
      loadingType: 'spinner',
    })

    if (request.method === 'get') {
      // 添加时间戳参数，防止浏览器（IE）对 get 请求的缓存
      request.params = {
        ...request.params,
        t: new Date().getTime(),
      }
    }

    return request
  },
  (error) => {
    return error
  }
)

// 响应拦截
instance.interceptors.response.use(
  (response) => {
    if (loadingInstance) {
      loadingInstance.clear()
    }

    let code = response.data.code
    let status = response.status

    // 如果返回的状态码为 200，说明接口请求成功，可以正常拿到数据
    if (status === 200) {
      // 请求成功
      if (code === 40001) {
        // 未登录
        let isTicket = false
        if (location.href.indexOf('/ticket') > -1) {
          isTicket = true
        }

        let url = `${process.env.VUE_APP_LOGIN_URL}/#/login?appId=${
          isTicket
            ? process.env.VUE_APP_APPID_BY_TICKET
            : process.env.VUE_APP_APPID_BY_SHORE
        }&${location.href.split('?')[1]}`
        // 是否是开发环境
        if (process.env.NODE_ENV == 'development') {
          url += `&_local=${encodeURIComponent(location.href)}`
        }
        location.href = url
      } else {
        return Promise.resolve(response.data)
      }
    } else {
      // 错误的请求  抛出错误
      return Promise.reject(response)
    }
  },

  (error) => {
    if (loadingInstance) {
      loadingInstance.clear()
    }
    if (error.response) {
      // 根据请求失败的 http 状态码去给用户相应的提示

      return Promise.reject(error)
    } else {
      console.error(`请求错误${error}`)
      return Promise.reject(new Error('请求超时，请刷新重试'))
    }
  }
)

/**
 * get 方法，对应 get 请求
 * @param {String} url [请求的 url 地址]
 * @param {Object} params [请求时携带的参数]
 */
export function get(url, params) {
  return new Promise((resolve, reject) => {
    instance
      .get(url, {
        params: params,
      })
      .then((res) => {
        resolve(res)
      })
      .catch((err) => {
        reject(err)
      })
  })
}

/**
 * post 方法，对应 post 请求
 * @param {String} url [请求的 url 地址]
 * @param {Object} params [请求时携带的参数]
 */
export function post(url, params, option = {}) {
  return new Promise((resolve, reject) => {
    instance
      .post(url, params, option)
      .then((res) => {
        resolve(res)
      })
      .catch((err) => {
        reject(err)
      })
  })
}
/**
 * put 方法，对应 put 请求
 * @param {String} url [请求的 url 地址]
 * @param {Object} params [请求时携带的参数]
 */
export function put(url, params) {
  return new Promise((resolve, reject) => {
    instance
      .put(url, params)
      .then((res) => {
        resolve(res)
      })
      .catch((err) => {
        reject(err)
      })
  })
}
