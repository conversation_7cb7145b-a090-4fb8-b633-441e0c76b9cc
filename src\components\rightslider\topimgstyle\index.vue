<template>
  <div class="pictureWithQuestionStyle">
    <!-- 表单 -->
    <el-form label-width="100px" label-position="left" :model="datas">
      <!-- 选择模板 -->
      <titlecom title="背景图片" tips="建议尺寸：750*370像素，尺寸不固定时，图片将按照最短边等比缩放裁剪" />
      
      <!-- 图片上传 -->
      <upload v-model="datas.imgUrl" />

      <div class="bor" />
      <!-- 提示 -->
      <titlecom
        title="其他设置"
      />

      <!-- switch button选择是否展示问一问 -->
      <el-form-item label="问一问组件">
        <el-switch
          v-model="datas.isShowQuestion"
          active-text="展示"
          inactive-text="不展示"
          inline-prompt
        />
      </el-form-item>

    </el-form>
  </div>
</template>

<script>
import upload from '../uploadWithButton' //自定义图片上传

export default {
  name: 'topimgstyle',
  props: {
    datas: Object,
  },
  // data() {
  //   return {
  //   }
  // },
  created() {},
  // watch: {
  //   datas: {
  //     handler(newVal) {
  //       console.log('datas数据变化:', newVal);
  //     },
  //     deep: true // 深度监听对象内部属性变化
  //   }
  // },
  methods: {
    showUpload(type) {
      this.uploadImgDataType = type
      this.$refs.upload.showUpload()
    },

    // TODO:补充清空背景图片
    clear() {
      this.datas.imgUrl = ''
    },
  },
  components: { upload },
}
</script>

<style scoped lang="less">
.pictureWithQuestionStyle {
  .imgList {
    padding: 10px;
    margin: 15px 0;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 0 4px 0 rgba(10, 42, 97, 0.2);
    display: flex;
    position: relative;
    gap: 10px;
    .imgClose {
      position: absolute;
      z-index: 1;
      right: 10px;
      top: 10px;
      cursor: pointer;
      font-size: 19px;
    }
    .imgText {
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      justify-content: space-between;
      .input-type {
        :deep(input) {
          border: none;
          padding: 0 24px 0 0;
          height: 21px;
          line-height: 21px;
        }
      }
    }
  }
}
</style>
