<template>
  <div class="headerTop">
    <el-icon><CloseBold /></el-icon>
    {{ pageSetup.name }}
    <el-icon><MoreFilled /></el-icon>
  </div>
</template>

<script setup>
import { CloseBold, MoreFilled } from '@element-plus/icons-vue'
// eslint-disable-next-line no-undef
const props = defineProps(['pageSetup'])
</script>

<style lang="less" scoped>
.headerTop {
  width: 100%;
  height: 44px;
  background: #fff;
  display: flex;
  padding: 0 20px;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e6e6e6;
  font-size: 18px;
  font-weight: 600;
  color: #14131F;
}
</style>
