<template>
  <div class="productWorkshopstyle">
    <div class="ptext">
      该组件链接将跳转到商品工坊页面，用户可与小助手对话，智能规划专属的商品组合
    </div>
    <div class="imgBox">
      <img src="@/assets/images/productShop.png" alt="">
    </div>
  </div>
</template>
<script>
export default {
  name: 'productWorkshopstyle',
  props: {
    datas: Object,
  },
}
</script>
<style scoped lang="less">
.productWorkshopstyle {
  .ptext {
    color: #999;
  }
  .imgBox {
    text-align: center;
    img {
      margin-top: 20px;
      width: 260px;
    }
  }
}
</style>
