<template>
  <div class="select-type">
    <!-- 选择类型 -->
    <el-select
      class="select-left"
      v-model="props.element.linkType"
      placeholder="请选择类型"
      @change="
        (e) => {
          props.element.link = ''
          props.element.linkTitle = ''
        }
      "
    >
      <el-option
        v-for="(v, k) in linkTypeEnum"
        :key="k"
        :value="k"
        :label="v"
      ></el-option>
    </el-select>
    <!-- 外链 -->
    <el-input
      class="select-right"
      v-if="props.element.linkType == 8"
      placeholder="请输入"
      v-model="props.element.link"
    ></el-input>
    <!-- 内链 -->
    <el-select
      class="select-right"
      v-else-if="props.element.linkType == 0"
      v-model="props.element.link"
      placeholder="请选择"
    >
      <el-option
        v-for="(v, k) in systemPageEnum"
        :key="k"
        :value="k"
        :label="v"
      ></el-option>
    </el-select>
    <el-button
      :disabled="!props.element.linkType"
      class="select-button"
      plain
      v-else
      @Click="tableConfig[element.linkType].init({ current: 1, pageSize: 10 })"
      :style="props.element.linkTitle ? '' : 'color: #c0c4cc;'"
      >{{ props.element.linkTitle || '请选择' }}</el-button
    >
    <!-- 弹窗 -->
    <el-dialog
      :title="linkTypeEnum[element.linkType]"
      v-model="tableVisible"
      append-to-body
      destroy-on-close
    >
      <el-table
        :ref="singleTable"
        :data="tableData"
        highlight-current-row
        @current-change="tableConfig[element.linkType].finish"
      >
        <el-table-column
          v-for="v in tableConfig[element.linkType].columns"
          :key="v.dataIndex"
          :property="v.dataIndex"
          :label="v.title"
        >
          <template v-if="v.valueEnum || v.render" #default="scope">
            <span>{{
              v.valueEnum
                ? v.valueEnum[scope.row[v.dataIndex]]
                : v.render(scope.row)
            }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup>
import api from '@/api/api';
import { ref } from 'vue';

// URL 传参
const obj = {}
location.href
  .split('?')[1]
  .split('&')
  .map((item) => {
    obj[item.split('=')[0]] = item.split('=')[1]
  })
// 跳转类型
const linkTypeEnum = {
  0: '系统页面',
  1: '活动页面',
  2: '景区详情',
  3: '门票详情',
  4: '组合票详情',
  5: '权益卡详情',
  6: '文章详情',
  7: '导览详情',
  8: '自定义链接',
}
// 系统页面
const systemPageEnum = {
  home: '主页',
  tour: '全部导览',
  order: '全部订单',
  my: '个人中心',
  ticketList: '门票预订',
  travelCardList: '权益卡',
  information: '活动资讯',
  travelNotes: '攻略游记',
  tripguangyuan: '畅游广元',
  rightsCard: '权益优惠景区'
}
// eslint-disable-next-line no-undef
const props = defineProps(['element'])
const tableVisible = ref(false)
const tableData = ref([])
const singleTable = ref(null)
// 表格项
const tableConfig = {
  1: {
    columns: [
      {
        title: '页面名称',
        dataIndex: 'pageName',
      },
      {
        title: '页面状态',
        dataIndex: 'pageState',
        valueEnum: { 1: '未发布', 2: '已发布' },
      },
    ],
    init: ({ current, pageSize }) => {
      api
        .getPageList({ storeId: obj.storeId, current, pageSize })
        .then((res) => {
          tableData.value = res.data.data
          tableVisible.value = true
        })
    },
    finish: (v) => {
      props.element.link = v.id
      props.element.linkTitle = v.pageName
      tableVisible.value = false
    },
  },
  2: {
    columns: [
      {
        title: '景区名称',
        dataIndex: 'scenicName',
      },
      {
        title: '景区等级',
        dataIndex: 'scenicGradle',
        valueEnum: {
          0: '未评',
          1: '国家 A 级景区',
          2: '国家 2A 级景区',
          3: '国家 3A 级景区',
          4: '国家 4A 级景区',
          5: '国家 5A 级景区',
        },
      },
    ],
    init: ({ current, pageSize }) => {
      api
        .getScenicList({ storeId: obj.storeId, current, pageSize })
        .then((res) => {
          tableData.value = res.data.records
          tableVisible.value = true
        })
    },
    finish: (v) => {
      props.element.link = v.scenicId
      props.element.linkTitle = v.scenicName
      tableVisible.value = false
    },
  },
  // 单票
  3: {
    columns: [
      {
        title: '商品名称',
        dataIndex: 'goodsName',
      },
      {
        title: '类别',
        dataIndex: 'storeGoodsType',
        valueEnum: { 1: '单票', 2: '组合票' },
      },
      {
        title: '票种',
        dataIndex: 'goodsType',
        valueEnum: {
          0: '成人票',
          1: '儿童票',
          2: '老人票',
          3: '保险票',
          4: '全价票',
          5: '半价票',
          7: '团体票',
        },
      },
      {
        title: '库存',
        dataIndex: 'quantity',
      },
      {
        title: '购买有效时间',
        dataIndex: 'purchaseTime',
        hideInSearch: true,
        render: (row) =>
          row.purchaseBeginTime
            ? `${row.purchaseBeginTime} 至 ${row.purchaseEndTime}`
            : '-',
      },
      {
        title: '入园有效时间',
        dataIndex: 'time',
        hideInSearch: true,
        render: (row) =>
          row.dayBegin ? `${row.dayBegin} 至 ${row.dayEnd}` : '-',
      },
      {
        title: '分时预约时间',
        search: false,
        editable: false,
        render: (row) =>
          row.timeShareBeginTime && row.timeShareEndTime
            ? `${row.timeShareBeginTime} -  ${row.timeShareEndTime}`
            : '-',
      },
      {
        title: '供应商名称',
        dataIndex: 'supplierNames',
      },
    ],
    init: () => {
      api
        .getTicketList({
          storeId: obj.storeId,
        })
        .then((res) => {
          tableData.value = res.data
          tableVisible.value = true
        })
    },
    finish: (v) => {
      props.element.link = v.storeGoodsId
      props.element.linkTitle = v.goodsName
      tableVisible.value = false
    },
  },
  // 组合票
  4: {
    columns: [
      {
        title: '商品名称',
        dataIndex: 'name',
      },
      {
        title: '类别',
        dataIndex: 'storeGoodsType',
        valueEnum: { 1: '单票', 2: '组合票' },
      },
      {
        title: '票种',
        dataIndex: 'goodsType',
        valueEnum: {
          0: '成人票',
          1: '儿童票',
          2: '老人票',
          3: '保险票',
          4: '全价票',
          5: '半价票',
          7: '团体票',
        },
      },
      {
        title: '库存',
        dataIndex: 'quantity',
      },
      {
        title: '购买有效时间',
        dataIndex: 'purchaseTime',
        hideInSearch: true,
        render: (row) =>
          row.purchaseBeginTime
            ? `${row.purchaseBeginTime} 至 ${row.purchaseEndTime}`
            : '-',
      },
      {
        title: '入园有效时间',
        dataIndex: 'time',
        hideInSearch: true,
        render: (row) =>
          row.dayBegin ? `${row.dayBegin} 至 ${row.dayEnd}` : '-',
      },
      {
        title: '分时预约时间',
        search: false,
        editable: false,
        render: (row) =>
          row.timeShareBeginTime && row.timeShareEndTime
            ? `${row.timeShareBeginTime} -  ${row.timeShareEndTime}`
            : '-',
      },
      {
        title: '供应商名称',
        dataIndex: 'supplierNames',
      },
    ],
    init: () => {
      api
        .getComposeTicketList({
          storeId: obj.storeId,
        })
        .then((res) => {
          tableData.value = res.data.data
          tableVisible.value = true
        })
    },
    finish: (v) => {
      props.element.link = v.storeGoodsId
      props.element.linkTitle = v.name
      tableVisible.value = false
    },
  },
  // 权益卡
  5: {
    columns: [
      {
        title: '商品名称',
        dataIndex: 'goodsName',
      },
      {
        title: '类别',
        dataIndex: 'storeGoodsType',
        render: () => '权益卡',
      },
    ],
    init: () => {
      api.getTravelTicketList({ storeId: obj.storeId }).then((res) => {
        tableData.value = res.data.data.map((v) => v.travelCardUnitInfo)
        tableVisible.value = true
      })
    },
    finish: (v) => {
      props.element.rightsId = v.rightsId
      props.element.link = v.storeGoodsId
      props.element.linkTitle = v.goodsName
      tableVisible.value = false
    },
  },
  6: {
    columns: [
      {
        title: '文章名称',
        dataIndex: 'articleName',
      },
      {
        title: '文章类型',
        dataIndex: 'articleType',
        valueEnum: {
          1: '攻略',
          2: '资讯',
          3: '活动',
          4: '游记',
        },
      },
    ],
    init: ({ current, pageSize }) => {
      api
        .getArticleList({
          storeId: obj.storeId,
          current,
          pageSize,
          enableState: 2,
        })
        .then((res) => {
          tableData.value = res.data.data
          tableVisible.value = true
        })
    },
    finish: (v) => {
      props.element.link = v.id
      props.element.linkTitle = v.articleName
      tableVisible.value = false
    },
  },
  7: {
    columns: [
      {
        title: '导览名称',
        dataIndex: 'scenicName',
      },
      {
        title: '导览类型',
        dataIndex: 'type',
        valueEnum: {
          1: '内部',
          2: '外部',
        },
      },
    ],
    init: ({ current, pageSize }) => {
      api
        .getNavigationList({ storeId: obj.storeId, current, pageSize })
        .then((res) => {
          tableData.value = res.data
          tableVisible.value = true
        })
    },
    finish: (v) => {
      props.element.link = v.scenicId == '0' ? v.navigationUrl : v.scenicId
      props.element.linkTitle = v.scenicName
      tableVisible.value = false
    },
  },
}
</script>
<script>
export default {
  name: 'linkcom',
}
</script>

<style lang="less" scoped>
.select-type {
  display: flex;
  .select-left {
    width: 50%;
    :deep(input) {
      border-right-width: 0;
      border-radius: 4px 0 0 4px;
      text-overflow: ellipsis;
    }
  }
  .select-right {
    width: 50%;
    :deep(input) {
      border-radius: 0 4px 4px 0;
      text-overflow: ellipsis;
    }
  }
  .select-button {
    width: 50%;
    border-radius: 0 4px 4px 0;
    justify-content: left;
    padding: 0 11px;
    :deep(span) {
      width: 98px;
      display: block;
      text-align: left;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>
