<template>
  <div class="productgroupstyle">
    <!-- 表单 -->
    <el-form label-width="80px" label-position="left" :model="datas">
      <!-- 选择模板 -->
      <titlecom title="选择模板" />
      <typecom v-model="datas.groupType" :list="groups" />
      <div class="bor" />
      <!-- 提示 -->
      <div class="titleBox">
        <titlecom
          title="添加分组"
          tips="拖拽排序，最多添加 3 个分组，每组最多添加 10 个商品"
        />
        <el-button
          class="groupAdd"
          size="small"
          :disabled="datas.groupList.length > 2"
          @click="uploadInformation"
        >
          添加（{{ datas.groupList.length }}/3）
        </el-button>
      </div>
      <!-- 分组 -->
      <div class="group" v-if="datas.groupList[0]">
        <div
          class="groupItem"
          v-for="(groupItem, groupIndex) in datas.groupList"
          :key="groupIndex"
        >
          <el-input v-model="groupItem.text" placeholder="请输入标题"
            ><template #append
              ><van-icon
                name="delete-o"
                :disable="datas.groupList.length < 2"
                @click="deleteimg(datas.groupList, groupIndex)" /></template
          ></el-input>
          <vuedraggable
            :list="groupItem.goodsList"
            item-key="index"
            :forceFallback="true"
            :animation="200"
            class="goods"
          >
            <template #item="{ element, index }">
              <section class="goodsItem">
                <van-icon
                  class="close"
                  name="close"
                  @click="deleteimg(groupItem.goodsList, index)"
                />
                <img
                  class="img"
                  :src="imgSrc($store.state.goodsObj[element]?.picUrl)"
                />
                <div class="text">
                  <span>{{ $store.state.goodsObj[element]?.goodsName || $store.state.goodsObj[element]?.productName }}</span>
                  <span>¥ {{ $store.state.goodsObj[element]?.salePrice }}起</span>
                </div>
              </section>
            </template>
          </vuedraggable>
          <el-button
            class="imgAdd"
            :disabled="groupItem.goodsList.length > 9"
            @click="uploadInformationGoods(groupItem.goodsList)"
          >
            添加商品（{{ groupItem.goodsList.length }}/10）
          </el-button>
        </div>
      </div>
      <div class="bor"></div>
      <titlecom title="组件样式" />
      <!-- 文字样式 -->
      <el-form-item label="文字样式">
        <el-radio-group v-model="datas.textType">
          <el-radio label="routine">常规</el-radio>
          <el-radio label="bold">加粗</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- 文字位置 -->
      <el-form-item label="文字位置">
        <el-radio-group v-model="datas.pointType">
          <el-radio label="left">左对齐</el-radio>
          <el-radio label="center">居中</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- 图片比例 -->
      <el-form-item label="图片比例">
        <el-select
          style="width: 100%"
          v-model="datas.scaleType"
          placeholder="请选择"
        >
          <el-option
            v-for="(value, key) in scales"
            :key="key"
            :value="key"
            :label="value"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- 填充样式 -->
      <el-form-item label="填充样式">
        <el-radio-group v-model="datas.aspectType">
          <el-radio label="fill">填充</el-radio>
          <el-radio label="fit">留白</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- 图片圆角 -->
      <el-form-item label="图片圆角">
        <el-slider v-model="datas.borderRadius" :max="55" show-input>
        </el-slider>
      </el-form-item>
      <!-- 商品间距 -->
      <el-form-item label="商品间距">
        <el-slider v-model="datas.productMargin" :max="30" show-input>
        </el-slider>
      </el-form-item>
      <!-- 页面边距 -->
      <el-form-item label="页面边距">
        <el-slider v-model="datas.pageMargin" :max="30" show-input> </el-slider>
      </el-form-item>
      <!-- 商品价格 -->
      <el-form-item label="商品价格">
        <el-switch
          v-model="datas.priceSwitch"
          inline-prompt
          active-text="开"
          inactive-text="关"
        />
      </el-form-item>
      <!-- 商品名称 -->
      <el-form-item label="商品名称">
        <el-switch
          v-model="datas.textSwitch"
          inline-prompt
          active-text="开"
          inactive-text="关"
        />
      </el-form-item>
    </el-form>
    <!-- 弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="添加商品"
      destroy-on-close
      :width="1400"
      :close-on-click-modal="false"
    >
      <el-form :inline="true" :model="form" class="searchForm">
        <el-form-item label="商品名称">
          <el-input v-model="form.goodsName" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="景区名称">
          <el-input v-model="form.scenicName" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="商品类别">
          <el-select
            style="width: 200px"
            v-model="form.ticketType"
            placeholder="请选择"
            clearable
          >
            <el-option label="单票" value="1" />
            <el-option label="组合票" value="3" />
            <el-option label="权益卡" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item style="margin-left: auto">
          <el-button type="primary" @click="getShopList(1)">查询</el-button>
        </el-form-item>
      </el-form>
      <el-table ref="tableRef" :data="tableData">
        <el-table-column type="selection" width="55" />
        <el-table-column
          v-for="v in tableColumns"
          :key="v.dataIndex"
          :property="v.dataIndex"
          :label="v.title"
        >
          <template v-if="v.valueEnum || v.render" #default="scope">
            <span>{{
              v.valueEnum
                ? v.valueEnum[scope.row[v.dataIndex]]
                : v.render(scope.row)
            }}</span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        class="paging"
        background
        layout="prev, pager, next"
        :total="total"
        :current-page="currentPage"
        @current-change="getShopList"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="updateGoods"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import api from '@/api/api';
import vuedraggable from 'vuedraggable'; //拖拽组件

const obj = {}
location.href
  .split('?')[1]
  .split('&')
  .map((item) => {
    obj[item.split('=')[0]] = item.split('=')[1]
  })

export default {
  name: 'productgroupstyle',
  props: {
    datas: Object,
  },
  data() {
    return {
      form: {
        goodsName: '',
        scenicName: '',
        ticketType: null,
      },
      groups: [
        {
          name: '横向滑动',
          value: '0',
          icon: 'product_type_0',
        },
        {
          name: '一行两个',
          value: '1',
          icon: 'product_type_1',
        },
      ],
      scales: { 1: '1:1', 0: '3:2', 3: '16:9' },
      dialogVisible: false,
      total: 0,
      currentPage: 1,
      tableData: [],
      tableColumns: [
        {
          title: '景区名称',
          dataIndex: 'scenicName',
        },
        {
          title: '商品名称',
          dataIndex: 'goodsName',
        },
        {
          title: '类别',
          dataIndex: 'storeGoodsType',
          valueEnum: { 1: '组合票', 2: '单票', 3: '权益卡' },
        },
        {
          title: '票种',
          dataIndex: 'goodsType',
          render: (row) => row.goodsType ? row.goodsType.split(',').map(key => {
            return {
              0: '成人票',
              1: '儿童票',
              2: '老人票',
              3: '保险票',
              4: '全价票',
              5: '半价票',
              7: '团体票',
            }[key]
          }).join(' ') : '-'
        },
        {
          title: '数字资产',
          dataIndex: 'isDigit',
          render: (row) => row.isDigit.trim() ? {
            0: '否',
            1: '是',
          }[row.isDigit] : '-'
        },
        {
          title: '购买有效时间',
          dataIndex: 'purchaseTime',
          render: (row) =>
            row.purchaseBeginTime
              ? `${row.purchaseBeginTime} 至 ${row.purchaseEndTime}`
              : '-',
        },
        {
          title: '入园有效时间',
          dataIndex: 'time',
          render: (row) =>
            row.dayBegin ? `${row.dayBegin} 至 ${row.dayEnd}` : '-',
        },
        {
          title: '分时预约',
          dataIndex: 'timeShareVoList',
          render: (row) => row.timeShareVoList?.length ? row.timeShareVoList.map(item => `${item.beginTime} - ${item.endTime}`).join(' ') : '-'
        },
        {
          title: '供应商名称',
          dataIndex: 'supplierNames',
        },
        {
          title: '库存',
          dataIndex: 'quantity',
        },
        {
          title: '用户售价（元）',
          dataIndex: 'sellingPrice',
          render: (row) => row.sellingPrice ? `${row.sellingPrice} 起` : '-'
        },
      ],
      goodsList: [],
    }
  },
  methods: {
    imgSrc(src) {
      return src ? process.env.VUE_APP_IMG_HOST + src?.split(',')[0] : require('@/assets/images/pic.svg')
    },
    // 提交
    uploadInformation() {
      this.datas.groupList.push({
        text: '',
        goodsList: [],
      })
    },
    // 商品列表
    getShopList(current) {
      api
        .getStoreGoodsList({
          ...this.form,
          storeId: obj.storeId,
          isEnable: 1,
          current,
          pageSize: 10,
        })
        .then((res) => {
          this.tableData = res.data.data
          this.total = res.data.total
          this.currentPage = res.data.current
          this.dialogVisible = true
        })
    },
    // 商品弹窗
    uploadInformationGoods(goodsList) {
      this.goodsList = goodsList
      this.getShopList(1)
    },
    updateGoods() {
      const list = this.$refs.tableRef.getSelectionRows()
      list.forEach((element) => {
        element.salePrice = element.sellingPrice
        this.$store.state.goodsObj[element.storeGoodsId] = element
        this.goodsList.push(element.storeGoodsId)
        this.dialogVisible = false
      })
    },
    /* 删除图片列表的图片 */
    deleteimg(list, index) {
      list.splice(index, 1)
    },
  },
  // created() {
  //   const storeGoodsIds = []
  //   this.datas.groupList.forEach((item) => {
  //     storeGoodsIds.push(...item.goodsList)
  //   })
  //   api
  //     .getDesignerGoodsList({
  //       storeId: obj.storeId,
  //       storeGoodsIds,
  //     })
  //     .then(({ data }) => {
  //       data.forEach((item) => {
  //         this.$store.state.goodsObj[item.storeGoodsId] = item
  //       })
  //     })
  // },
  components: { vuedraggable },
}
</script>

<style scoped lang="less">
.productgroupstyle {
  .titleBox {
    position: relative;
    .groupAdd {
      position: absolute;
      top: 0;
      right: 0;
    }
  }
  .group {
    display: flex;
    flex-direction: column;
    gap: 20px;
    .groupItem {
      .goods {
        min-height: 15px;
        .goodsItem {
          padding: 10px;
          margin: 15px 0;
          border-radius: 4px;
          background-color: #fff;
          box-shadow: 0 0 4px 0 rgba(10, 42, 97, 0.2);
          display: flex;
          position: relative;
          gap: 10px;
          .close {
            position: absolute;
            z-index: 1;
            right: 10px;
            top: 10px;
            cursor: pointer;
            font-size: 19px;
          }
          .img {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 4px;
          }
          .text {
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
            justify-content: space-between;
            .input-type {
              :deep(input) {
                border: none;
                padding: 0 24px 0 0;
                height: 21px;
                line-height: 21px;
              }
            }
          }
        }
      }
      .imgAdd {
        width: 100%;
        height: 40px;
        border-style: dashed;
      }
    }
  }
  .searchForm {
    display: flex;
    padding: 0 20px;
  }
  .paging {
    margin-top: 16px;
    justify-content: end;
  }
}
</style>
