<template>
  <div class="articlemanagestyle">
    <!-- 表单 -->
    <el-form label-width="80px" label-position="left" :model="datas">
      <!-- 组件名称 -->
      <el-form-item label="组件名称">
        <el-input
          v-model="datas.text"
          maxlength="10"
          placeholder="请输入"
          show-word-limit
          type="text"
        />
      </el-form-item>
      <!-- 文章类型 -->
      <el-form-item label="文章类型">
        <el-select
          multiple
          style="width: 100%"
          v-model="sourcesValue"
          placeholder="请选择"
          @change="(e) => (datas.sourceType = e.join(','))"
        >
          <el-option
            v-for="(value, key) in sources"
            :key="key"
            :value="key"
            :label="value"
          ></el-option>
        </el-select>
      </el-form-item>
      <div class="bor"></div>
      <!-- 选择模板 -->
      <titlecom title="列表样式" />
      <typecom v-model="datas.articleType" :list="articles" />
      <div class="bor"></div>
      <titlecom title="组件样式" />
      <!-- 卡片样式 -->
      <el-form-item label="卡片样式">
        <el-radio-group v-model="datas.cardType">
          <el-radio label="white">无边白底</el-radio>
          <el-radio label="projection">卡片投影</el-radio>
          <el-radio label="outline">描边白底</el-radio>
          <el-radio label="transparent" :disabled="datas.articleType != 'large'"
            >透明底</el-radio
          >
        </el-radio-group>
      </el-form-item>
      <!-- 文字样式 -->
      <el-form-item label="文字样式">
        <el-radio-group v-model="datas.textType" class="radi1">
          <el-radio label="routine">常规</el-radio>
          <el-radio label="bold">加粗</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- 查看更多 -->
      <el-form-item label="查看更多">
        <el-select
          style="width: 100%"
          v-model="datas.moreType"
          placeholder="请选择"
        >
          <el-option
            v-for="(value, key) in mores"
            :key="key"
            :value="key"
            :label="value"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- 图片圆角 -->
      <el-form-item label="图片圆角">
        <el-slider v-model="datas.borderRadius" :max="55" show-input>
        </el-slider>
      </el-form-item>
      <!-- 图片间距 -->
      <el-form-item label="图片间距">
        <el-slider v-model="datas.imageMargin" :max="30" show-input>
        </el-slider>
      </el-form-item>
      <!-- 页面边距 -->
      <el-form-item label="页面边距">
        <el-slider v-model="datas.pageMargin" :max="30" show-input> </el-slider>
      </el-form-item>
      <!-- 作者 -->
      <el-form-item label="作者" v-show="datas.articleType != 'large'">
        <el-switch
          v-model="datas.authorSwitch"
          inline-prompt
          active-text="开"
          inactive-text="关"
        />
      </el-form-item>
      <!-- 阅读数 -->
      <el-form-item label="阅读数" v-show="datas.articleType != 'large'">
        <el-switch
          v-model="datas.viewsSwitch"
          inline-prompt
          active-text="开"
          inactive-text="关"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'articlemanagestyle',
  props: {
    datas: Object,
  },
  data() {
    return {
      articles: [
        {
          name: '瀑布流',
          value: 'flow',
          icon: 'article_type_0',
        },
        {
          name: '左右图文',
          value: 'around',
          icon: 'article_type_1',
        },
        {
          name: '大图列表',
          value: 'large',
          icon: 'article_type_2',
        },
      ],
      sources: {
        1: '攻略',
        2: '资讯',
        3: '活动',
        4: '游记',
      },
      sourcesValue: [],
      mores: { 0: '上方显示', 1: '下方显示', 2: '隐藏' },
    }
  },
  created() {
    this.sourcesValue = this.datas.sourceType.split(',')
  },
}
</script>
