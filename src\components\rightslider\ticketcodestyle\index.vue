<template>
  <div class="decorate">
    <!-- 表单 -->
    <el-form label-width="80px" :model="datas" :rules="rules">
      <titlecom title="位置及尺寸" tips="可拖拽或拉伸组件，调整位置及尺寸" />
      <el-form-item label="横向位置">
        <el-slider
          v-model="datas.left"
          :min="0"
          :max="pageSetup.width"
          show-input
        ></el-slider>
      </el-form-item>
      <el-form-item label="纵向位置">
        <el-slider
          v-model="datas.top"
          :min="0"
          :max="pageSetup.height"
          show-input
        ></el-slider>
      </el-form-item>
      <el-form-item label="组件宽度">
        <el-slider
          v-model="datas.width"
          :min="0"
          :max="700"
          show-input
        ></el-slider>
      </el-form-item>
      <el-form-item label="组件高度">
        <el-slider
          v-model="datas.height"
          :min="0"
          :max="700"
          show-input
        ></el-slider>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'ticketcodestyle',
  props: {
    datas: Object,
    pageSetup: Object,
  },
  data() {
    return {
      rules: {
        // 校验表单输入
        name: [
          // 页面名称
          { required: true, message: '请输入页面名称', trigger: 'blur' },
        ],
        details: [
          // 页面描述
          { required: true, message: '请输入页面描述', trigger: 'blur' },
        ],
      },
      pickeShow: false, // 颜色选择器是否显示
      predefineColors: [
        // 颜色选择器预设
        'rgba(249, 249, 249, 10)',
      ],
    }
  },
  setup() {
    return {}
  },
  methods: {},
}
</script>

<style scoped lang="less">
/* 页面设置 */
.decorate {
}
</style>
