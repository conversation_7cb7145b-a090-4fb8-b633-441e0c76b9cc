<template>
  <el-upload
    class="upload"
    action="/scenic/api-v2/aws/uploadFile"
    :show-file-list="false"
    :data="{ type: 'image' }"
    :on-success="handleAvatarSuccess"
    :on-error="handleAvatarError"
    :before-upload="beforeAvatarUpload"
  >
    <div v-if="imageUrl" class="avatar"><img :src="imageUrl" /></div>
    <el-button v-else class="imgAdd">
      <el-icon><Plus /></el-icon> 添加图片
    </el-button>
  </el-upload>
</template>

<script setup>
import { Plus } from '@element-plus/icons-vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { ref, watch } from 'vue'
const ALLOWED_IMAGE_FORMATS = ['jpg', 'jpeg', 'png']
const ALLOWED_FORMATS_DISPLAY = ALLOWED_IMAGE_FORMATS.map((fmt) =>
  fmt.toUpperCase()
).join(', ')
// eslint-disable-next-line no-undef
const props = defineProps(['modelValue'])
// eslint-disable-next-line no-undef
const emits = defineEmits(['update:modelValue'])
let imageUrl = ref()
const handleAvatarSuccess = (res) => {
  emits('update:modelValue', process.env.VUE_APP_IMG_URL + res.data.path)
}
const handleAvatarError = (res) => {
  // 弹窗提示
  ElMessage.error('上传失败')
}
// 上传前严格验证
const beforeAvatarUpload = (file) => {
  // 1. 从文件名获取扩展名并转换为小写
  const fileName = file.name.toLowerCase()
  const ext = fileName.split('.').pop()

  // 2. 验证文件扩展名
  if (!ALLOWED_IMAGE_FORMATS.includes(ext)) {
    ElMessage.error(`只能上传以下图片格式：${ALLOWED_FORMATS_DISPLAY}`)
    return false
  }

  // 3. 验证MIME类型（双重保险）
  const isImage = file.type.startsWith('image/')
  if (!isImage) {
    ElMessage.error('文件类型错误，请上传图片文件')
    return false
  }

  // 4. 验证文件大小 (5MB)
  const maxSize = 5 * 1024 * 1024 // 5MB
  if (file.size > maxSize) {
    ElMessage.error('图片大小不能超过5MB')
    return false
  }
  const newFileName = 'new_name.' + file.name.split('.').pop()
  const newFile = new File([file], newFileName, { type: file.type })

  // 本地模拟（实际使用时移除）
  // emits(
  //   'update:modelValue',
  //   'https://prod.shukeyun.com/maintenance/deepfile/data/2025-08-13/upload_ee382a1c76fc3dcbcf424b0f297b1024.png'
  // )

  return newFile
}

watch(
  props,
  () => {
    imageUrl.value = props.modelValue
  },
  { immediate: true }
)
</script>

<style scoped lang="less">
.upload {
  display: flex;
  .avatar,
  .icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    box-sizing: border-box;
    overflow: hidden;
  }
  .avatar {
    border: 1px solid #dcdfe6;
    position: relative;
    > img {
      width: 100%;
      height: 100%;
    }
    &::after {
      content: '更换';
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 20px;
      background: #000;
      text-align: center;
      line-height: 20px;
      font-size: 12px;
      color: #fff;
      opacity: 0;
      transition: 0.5s;
    }
  }
  .icon {
    border: 1px dashed #dcdfe6;
    transform: 0.2s;
    font-size: 28px;
    color: #8c939d;
    cursor: pointer;
    transition: 0.5s;
  }
  &:hover {
    .avatar::after {
      opacity: 0.63;
    }
    .icon {
      border-color: #337ecc;
    }
  }
  .imgAdd {
    width: 320px;
    height: 40px;
    border-style: dashed;
  }
}
</style>
