<template>
  <div class="graphicnavigation">
    <!-- 默认导航 -->
    <section
      class="defaultNavigation"
      v-if="!datas.navigationList[0]"
      :style="{
        overflow: datas.navigationType == 1 ? 'auto' : 'hidden',
        flexWrap: datas.navigationType == 1 ? 'wrap' : 'nowrap',
        // background: datas.backgroundColor,
        // display: datas.navigationType == 1 ? 'flex' : '-webkit-box',
        // 'overflow-x': datas.navigationType == 1 ? '' : 'scroll',
      }"
    >
      <!-- 导航 -->
      <div
        class="navigationList"
        v-for="index in datas.navigationType == 1 ? 10 : 4"
        :key="index"
        :style="{
          flexBasis: [6, 7, 8].includes(datas.navigationList.length)
            ? '25%'
            : '20%',
          width:
            datas.navigationType == 1
              ? 'auto'
              : 375 / datas.showSize - 1 + 'px',
        }"
      >
        <!-- 图片 -->
        <img
          src="../../../assets/images/imgs.png"
          alt="默认图片"
          draggable="false"
          :style="{ 'border-radius': datas.borderRadius + '%' }"
        />
        <!-- 文字 -->
        <p :style="{ color: datas.textColor }">导航</p>
      </div>
    </section>

    <!-- 导航列表 -->
    <section
      class="defaultNavigation"
      v-else
      :style="{
        overflow: datas.navigationType == 1 ? 'auto' : 'hidden',
        flexWrap: datas.navigationType == 1 ? 'wrap' : 'nowrap',
        // background: datas.backgroundColor,
        // display: datas.navigationType == 1 ? 'flex' : '-webkit-box',
        // 'flex-wrap': datas.navigationType == 1 ? 'wrap' : 'nowrap',
        // 'justify-content':
        //   datas.navigationType == 1 ? 'space-evenly' : 'space-around',
        // 'overflow-x': datas.navigationType == 1 ? '' : 'scroll',
      }"
    >
      <!-- 导航 -->
      <div
        class="navigationList"
        v-for="(item, index) in datas.navigationList"
        :key="index"
        :style="{
          flexBasis: [6, 7, 8].includes(datas.navigationList.length)
            ? '25%'
            : '20%',
          width:
            datas.navigationType == 1 ? '20%' : 375 / datas.showSize - 1 + 'px',
        }"
      >
        <!-- 图片 -->
        <img
          :src="item.src || defaultImg"
          alt="默认图片"
          draggable="false"
          :style="{ 'border-radius': datas.borderRadius + '%' }"
        />
        <!-- 文字 -->
        <p
          :style="{
            maxWidth: '67px',
            color: datas.textColor,
            'font-size': datas.textSize + 'px',
            height: datas.textHeight + 'px',
            'line-height': datas.textHeight + 'px',
          }"
        >
          {{ item.text || '标题' }}
        </p>
      </div>
    </section>

    <div
      v-if="datas.navigationType == 0 && datas.navigationList.length > 5"
      style="
        width: 36px;
        height: 4px;
        border-radius: 2px;
        background: #ccc;
        margin: 4px auto 0;
      "
    >
      <span
        style="
          height: 100%;
          border-radius: 2px;
          background: #349fff;
          display: block;
        "
        :style="`width: calc(4/${datas.navigationList.length} * 36px);`"
      ></span>
    </div>

    <!-- 删除组件 -->
    <slot name="deles" />
  </div>
</template>

<script>
export default {
  name: 'graphicnavigation',
  props: {
    datas: Object,
  },
  data() {
    return {
      defaultImg: require('@/assets/images/default_icon.svg'),
    }
  },
  created() {
    console.log(this.datas, '--------graphicnavigation')
  },
}
</script>

<style scoped lang="less">
.graphicnavigation {
  position: relative;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  /* 默认导航 */
  .defaultNavigation {
    display: flex;
    flex-wrap: wrap;
    gap: 8px 0;
    // // overflow-x: scroll;
    // justify-content: space-evenly;
    // &::-webkit-scrollbar {
    //   height: 1px;
    // }
    // &::-webkit-scrollbar-thumb {
    //   background-color: #1890FF;
    // }
    // :deep(.el-collapse-item__header),
    // :deep(.el-collapse-item__wrap) {
    //   border-bottom: 0 !important;
    // }
    /* 导航 */
    .navigationList {
      display: flex;
      flex-direction: column;
      flex-grow: 1;
      flex-shrink: 0;
      flex-basis: 20%;
      align-items: center;
      &:nth-child(n + 5) {
        flex-grow: 0;
      }
      img {
        width: 55px;
        height: 55px;
      }
      p {
        width: 100%;
        font-size: 13px;
        font-weight: bold;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-align: center;
        box-sizing: border-box;
      }
    }
  }
}
</style>
