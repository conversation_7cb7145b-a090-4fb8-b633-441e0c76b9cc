<template>
  <rightslidercom title="图片魔方">
    <div class="picturecubestyle">
      <!-- 表单 -->
      <el-form label-width="80px" :model="datas" size="small">
        <div class="line-title">魔方图片</div>
        <div class="line-dec">
          选定布局区域，在下方添加图片，建议添加比例一致的图片
        </div>
        <div class="cube-box">
          <div
            class="cube-item"
            style="
              border: 1px solid rgba(0, 0, 0, 0.15);
              position: absolute;
              top: 0;
              left: 0;
              width: 50%;
              height: 100%;
            "
          ></div>
        </div>

        <div class="grid-container">
          <div
            v-for="cell in 9"
            :key="cell"
            class="cell"
            :class="{ selected: isSelected(cell) }"
            @mouseenter="handleMouseEnter(cell)"
            @mousedown.prevent="startSelection(cell)"
            @mouseup="endSelection"
          ></div>
        </div>

        <!-- 标题内容 -->
        <el-form-item label="选择模板：" class="lef">
          <p style="color: #000">{{ styleText }}</p>
        </el-form-item>

        <!-- 轮播图选择 -->
        <div class="swiperType">
          <el-tooltip
            class="item"
            effect="dark"
            content="一行一个"
            placement="bottom"
          >
            <span
              class="iconfont icon-yihangyige"
              style="font-size: 21px"
              :class="datas.swiperType == 0 ? 'active' : ''"
              @click="datas.swiperType = '0'"
            />
          </el-tooltip>

          <el-tooltip
            class="item"
            effect="dark"
            content="轮播海报"
            placement="bottom"
          >
            <span
              class="iconfont icon-icon_tupian_lunbohaibao"
              style="font-size: 20px"
              :class="datas.swiperType == 1 ? 'active' : ''"
              @click="datas.swiperType = '1'"
            />
          </el-tooltip>

          <el-tooltip
            class="item"
            effect="dark"
            content="双层轮播"
            placement="bottom"
          >
            <span
              class="iconfont icon-daohanghengxianghuadong"
              style="font-size: 24px"
              :class="datas.swiperType == 2 ? 'active' : ''"
              @click="datas.swiperType = '2'"
            />
          </el-tooltip>

          <el-tooltip
            class="item"
            effect="dark"
            content="横向滑动"
            placement="bottom"
          >
            <span
              class="iconfont icon-xiaotuhengxianghuadong"
              style="font-size: 24px"
              :class="datas.swiperType == 3 ? 'active' : ''"
              @click="datas.swiperType = '3'"
            />
          </el-tooltip>
        </div>

        <!-- 下划线 -->
        <div class="bor" />
        <div class="line-title">添加图片</div>
        <div class="line-dec">sdfsdfsdfsdfsdf</div>

        <h5 style="color: #000; font-size: 14px">添加图片</h5>
        <p style="color: #969799; font-size: 12px; margin-top: 10px">
          拖动选中的导航可对其排序
        </p>

        <!-- 图片广告 -->
        <div v-if="datas.imageList[0]">
          <vuedraggable
            :list="datas.imageList"
            item-key="index"
            :forceFallback="true"
            :animation="200"
          >
            <template #item="{ element, index }">
              <section class="imgBanner">
                <van-icon
                  class="el-icon-circle-close"
                  name="close"
                  @click="deleteimg(index)"
                />
                <!-- 图片 -->
                <upload v-model="element.src" />
                <upload v-model="element.srcBg" v-if="datas.swiperType == 2" />
                <!-- <div class="imag">
                  <img :src="element.src" alt draggable="false" />
                </div> -->
                <!-- 标题和链接 -->
                <div class="imgText">
                  <el-input
                    v-model="element.text"
                    placeholder="图片提示信息"
                    size="small"
                  ></el-input>

                  <!-- 选择类型 -->
                  <div class="select-type">
                    <el-select
                      style="width: 60%"
                      v-model="element.linkType"
                      placeholder="类型"
                      size="small"
                      @change="
                        (e) => {
                          if (e == 1) {
                            element.link = ''
                          }
                        }
                      "
                    >
                      <el-option
                        v-for="element in optionsType"
                        :key="element.type"
                        :value="element.type"
                        :label="element.name"
                      ></el-option>
                    </el-select>

                    <!-- 输入链接 -->
                    <el-input
                      v-if="element.linkType == 1"
                      style="width: 100%"
                      size="small"
                      placeholder="请输入"
                      v-model="element.link"
                    ></el-input>
                    <!-- 选择内链 -->
                    <el-select
                      v-else
                      style="width: 100%"
                      v-model="element.link"
                      placeholder="请选择"
                      size="small"
                    >
                      <el-option
                        v-for="element in pagePaths"
                        :key="element.value"
                        :value="element.value"
                        :label="element.name"
                      ></el-option>
                    </el-select>
                  </div>
                </div>
              </section>
            </template>
          </vuedraggable>
        </div>
        <!-- 上传图片 -->
        <el-button
          v-if="datas.imageList.length < 10"
          @click="uploadInformation"
          class="uploadImg"
          type="primary"
          plain
        >
          点击添加图片
        </el-button>

        <!-- 下划线 -->
        <div class="bor"></div>

        <!-- <el-form-item
          class="lef"
          label="一行个数"
          v-show="datas.swiperType == 2"
        >
          <el-radio-group v-model="datas.rowindividual" class="radi">
            <el-radio :label="2">2 个</el-radio>
            <el-radio :label="3">3 个</el-radio>
            <el-radio :label="4">4 个</el-radio>
            <el-radio :label="5">5 个</el-radio>
            <el-radio :label="6">6 个</el-radio>
          </el-radio-group>
        </el-form-item>

        <div style="height: 10px" /> -->

        <!-- 自动轮播 -->
        <el-form-item
          label="自动轮播："
          class="lef"
          v-show="datas.swiperType == 1 || datas.swiperType == 2"
        >
          <el-switch
            v-model="datas.autoSwitch"
            inline-prompt
            active-text="是"
            inactive-text="否"
          />
        </el-form-item>

        <div style="height: 10px" />

        <!-- 切换间隔 -->
        <el-form-item
          label="切换间隔："
          class="lef"
          v-show="datas.swiperType == 1 || datas.swiperType == 2"
        >
          <el-select
            style="width: 100%"
            v-model="datas.durationType"
            placeholder="请选择"
          >
            <el-option
              v-for="(value, key) in durations"
              :key="key"
              :value="key"
              :label="value"
            ></el-option>
          </el-select>
        </el-form-item>

        <div style="height: 10px" />

        <!-- 轮播提示 -->
        <el-form-item
          label="轮播提示："
          class="lef"
          v-show="datas.swiperType == 1 || datas.swiperType == 2"
        >
          <el-select
            style="width: 100%"
            v-model="datas.indicatorType"
            placeholder="请选择"
          >
            <el-option
              v-for="(value, key) in indicators"
              :key="key"
              :value="key"
              :label="value"
            ></el-option>
          </el-select>
        </el-form-item>

        <div style="height: 10px" />

        <!-- 一屏展示 -->
        <el-form-item
          label="一屏展示："
          class="lef"
          v-show="datas.swiperType == 3"
        >
          <el-select
            style="width: 100%"
            v-model="datas.screenType"
            placeholder="请选择"
          >
            <el-option
              v-for="(value, key) in screenNums"
              :key="key"
              :value="key"
              :label="value"
            ></el-option>
          </el-select>
        </el-form-item>

        <div style="height: 10px" />

        <!-- 图片样式 -->
        <el-form-item label="图片样式：" class="lef borrediu">
          <el-radio-group v-model="datas.pictureType" class="radi1">
            <el-radio label="routine">常规</el-radio>
            <el-radio label="projection">投影</el-radio>
          </el-radio-group>
        </el-form-item>

        <div style="height: 10px" />

        <!-- 填充样式 -->
        <el-form-item
          label="填充样式："
          class="lef borrediu"
          v-show="datas.swiperType == 1 || datas.swiperType == 2"
        >
          <el-radio-group v-model="datas.aspectType" class="radi1">
            <el-radio label="fill">填充</el-radio>
            <el-radio label="fit">留白</el-radio>
          </el-radio-group>
        </el-form-item>

        <div style="height: 10px" />

        <!-- 图片圆角 -->
        <el-form-item label="图片圆角：" class="lef borrediu">
          <el-slider
            v-model="datas.borderRadius"
            :max="211"
            input-size="small"
            show-input
          ></el-slider>
        </el-form-item>

        <div style="height: 10px" />

        <!-- 图片间距 -->
        <el-form-item
          class="lef"
          label="图片间距"
          v-show="datas.swiperType == 0 || datas.swiperType == 3"
        >
          <el-slider
            v-model="datas.imageMargin"
            :max="30"
            input-size="small"
            show-input
          ></el-slider>
        </el-form-item>

        <div style="height: 10px" />

        <!-- 页面边距 -->
        <el-form-item class="lef" label="页面边距：">
          <el-slider
            v-model="datas.pageMargin"
            :max="30"
            input-size="small"
            show-input
          ></el-slider>
        </el-form-item>

        <div style="height: 10px" />

        <!-- 组件间距 -->
        <el-form-item class="lef" label="组件间距：">
          <el-slider
            v-model="datas.moduleMargin"
            :max="30"
            :min="datas.swiperType == 3 && datas.screenType == 1 ? -105 : -211"
            input-size="small"
            show-input
          ></el-slider>
        </el-form-item>
      </el-form>

      <!-- 上传图片 -->
      <!-- <uploadimg ref="upload" @uploadInformation="uploadInformation" /> -->
    </div>
  </rightslidercom>
</template>

<script>
import vuedraggable from 'vuedraggable' //拖拽组件
import upload from '../../upload' //图片上传

export default {
  name: 'picturecubestyle',
  components: { vuedraggable, upload },
  props: {
    datas: Object,
  },
  data() {
    return {
      isSelecting: false,
      selectedCells: [],
      finalSelectedCells: [],

      // 切换间隔
      durations: { 0: '3 秒', 1: '4 秒', 2: '5 秒' },
      // 跳转类型
      optionsType: [
        {
          type: '0',
          name: '内链',
        },
        {
          type: '1',
          name: '外链',
        },
      ],
      // 跳转页面
      pagePaths: [
        {
          name: '门票预订',
          value: 'ticketList',
        },
        {
          name: '权益卡',
          value: 'travelCardList',
        },
        {
          name: '活动资讯',
          value: 'information',
        },
        {
          name: '攻略游记',
          value: 'travelNotes',
        },
      ],
      // 指示器类型
      indicators: { 0: '样式一', 1: '样式二', 2: '样式三', 3: '样式四' },
      screenNums: { 0: '1 个图片', 1: '2 个图片' },
      emptyText: '',
    }
  },

  created() {},

  methods: {
    startSelection(cell) {
      if (!this.isSelecting) {
        // 开始选择
        this.isSelecting = true
        this.selectedCells = [cell]
      } else {
        // 选择结束
        this.isSelecting = false
        console.log(this.finalSelectedCells)
      }
    },
    handleMouseEnter(cell) {
      console.log('handleMouseEnter')
      if (this.isSelecting) {
        this.selectedCells[1] = cell
        this.calculateMinimumRectangle()
      }
    },
    endSelection() {
      // if (this.isSelecting) {
      //   this.isSelecting = false
      // }
    },
    calculateMinimumRectangle() {
      const rows = this.selectedCells.map((cell) => Math.ceil(cell / 3))
      const cols = this.selectedCells.map((cell) => cell % 3 || 3)
      const minRow = Math.min(...rows)
      const maxRow = Math.max(...rows)
      const minCol = Math.min(...cols)
      const maxCol = Math.max(...cols)

      this.finalSelectedCells = []
      for (let row = minRow; row <= maxRow; row++) {
        for (let col = minCol; col <= maxCol; col++) {
          const index = (row - 1) * 3 + col
          this.finalSelectedCells.push(index)
        }
      }
    },
    isSelected(cell) {
      return this.finalSelectedCells.includes(cell)
    },

    // 提交
    uploadInformation() {
      this.datas.imageList.push({
        text: '',
        src: '',
        srcBg: '',
        link: '',
        linkType: '1',
      })
    },

    /* 删除图片 */
    deleteimg(index) {
      this.datas.imageList.splice(index, 1)
    },
  },

  computed: {
    styleText() {
      let data
      if (this.datas.swiperType == 0) data = '一行一个'
      if (this.datas.swiperType == 1) data = '轮播海报'
      if (this.datas.swiperType == 2) data = '双层轮播'
      if (this.datas.swiperType == 3) data = '横向滑动'
      // if (this.datas.swiperType == 4) data = '导航横向滑动'

      return data
    },
  },
}
</script>

<style scoped lang="less">
.pictureadsstyle {
  .lef {
    :deep(.el-form-item__label) {
      text-align: left;
    }
  }

  /* 轮播图样式 */
  .swiperType {
    display: flex;
    justify-content: space-around;
    align-items: center;
    span {
      display: inline-block;
      width: 58px;
      height: 32px;
      text-align: center;
      line-height: 32px;
      background: #ebedf0;
      color: #979797;
      border: 1px solid #fff;
      cursor: pointer;
      transition: all 0.5s;

      &:hover {
        border: 1px solid #1890ff;
        color: #1890ff;
      }

      &.active {
        border: 1px solid #1890ff;
        background-color: #e0edff;
        color: #1890ff;
      }
    }
  }

  /* 圆角 */
  .borrediu {
    span {
      display: inline-block;
      width: 48px;
      height: 26px;
      text-align: center;
      line-height: 26px;
      background: #ebedf0;
      color: #979797;
      border: 1px solid #fff;
      cursor: pointer;
      transition: all 0.5s;

      &:hover {
        border: 1px solid #1890ff;
        color: #1890ff;
      }

      &.active {
        border: 1px solid #1890ff;
        background-color: #e0edff;
        color: #1890ff;
      }
    }
  }

  :deep(.radi) .el-radio {
    margin-right: 8px;
  }

  :deep(.radi1) .el-radio {
    margin-right: 7px;
    .el-radio__label {
      padding-left: 5px;
    }
  }

  /* 上传图片按钮 */
  .uploadImg {
    width: 100%;
    height: 40px;
    margin-top: 20px;
  }

  // 上传弹框内容部分
  :deep(.uploadIMG) .el-dialog__body {
    height: 280px;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    justify-content: center;
  }

  .disable {
    :deep(.el-upload) {
      display: none !important;
    }
  }

  /* 图片广告列表 */
  .imgBanner {
    padding: 6px 12px;
    margin: 16px 7px;
    border-radius: 2px;
    background-color: #fff;
    box-shadow: 0 0 4px 0 rgba(10, 42, 97, 0.2);
    display: flex;
    position: relative;
    gap: 10px;

    /* 删除图标 */
    .el-icon-circle-close {
      position: absolute;
      right: -10px;
      top: -10px;
      cursor: pointer;
      font-size: 19px;
    }

    /* 图片 */
    .imag {
      width: 60px;
      height: 60px;
      border-radius: 5px;
      overflow: hidden;
      position: relative;
      cursor: pointer;
      img {
        width: 100%;
        height: 100%;
        display: inline-block;
      }
      span {
        background: rgba(0, 0, 0, 0.5);
        font-size: 12px;
        position: absolute;
        left: 0px;
        bottom: 0px;
        display: inline-block;
        width: 100%;
        text-align: center;
        color: #fff;
        height: 20px;
        line-height: 20px;
      }
    }

    /* 图片字 */
    .imgText {
      width: 80%;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      justify-content: space-between;
      .select-type {
        display: flex;
        :deep(.el-select) {
          .el-input {
            input {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
  }
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 4px;
  width: 150px;
  user-select: none;
}

.cell {
  padding-top: 100%; /* 保持格子的正方形 */
  position: relative;
  border: 1px solid #ccc;
}

.cell.selected::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 255, 0.5);
}

.cube-box {
  position: relative;
  height: 188px;
  &::after {
    content: '';
    position: absolute;
    inset: 0;
    border: 1px solid rgba(0, 0, 0, 0.15);
  }
  .cube-item {
    position: relative;
    &::after {
      content: '';
      position: absolute;
      // inset: 0;
      border: 1px solid rgba(0, 0, 0, 0.15);
    }
  }
}
</style>
