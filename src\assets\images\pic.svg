<?xml version="1.0" encoding="UTF-8"?>
<svg width="237px" height="133px" viewBox="0 0 237 133" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 5备份 3@3x</title>
    <defs>
        <rect id="path-1" x="0" y="0" width="237" height="133"></rect>
        <filter color-interpolation-filters="auto" id="filter-3">
            <feColorMatrix in="SourceGraphic" type="matrix" values="0 0 0 0 0.838513 0 0 0 0 0.838513 0 0 0 0 0.838513 0 0 0 1.000000 0"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="组合票" transform="translate(-986.000000, -882.000000)">
            <g id="编组-5备份-3" transform="translate(986.000000, 882.000000)">
                <mask id="mask-2" fill="white">
                    <use xlink:href="#path-1"></use>
                </mask>
                <use id="矩形备份-4" fill="#EFEFEF" xlink:href="#path-1"></use>
                <g filter="url(#filter-3)" id="编组" opacity="0.976795015">
                    <g mask="url(#mask-2)">
                        <g transform="translate(79.000000, 27.000000)" fill="#000000" fill-rule="nonzero">
                            <path d="M71,28.3587376 L71,20.0502459 C71,18.1120606 69.4170864,16.540851 67.4644635,16.540851 L16.6408248,16.540851 C15.7031325,16.540851 14.8038406,16.9105685 14.1407867,17.5687093 C13.4777329,18.2268501 13.1052322,19.1194869 13.1052322,20.0502459 L13.1052322,28.3237077 C17.7288412,28.4291177 21.4216646,32.179793 21.4216646,36.7704255 C21.4216646,41.361058 17.7288412,45.1117333 13.1052322,45.2171433 L13.1052322,53.4906608 C13.1052322,54.4214102 13.4777543,55.3140327 14.1408066,55.9721614 C14.8038588,56.6302902 15.7031423,57 16.6408248,57 L67.4644635,57 C68.4021461,57 69.3014236,56.6302612 69.9644653,55.972122 C70.627507,55.3139828 71,54.4213545 71,53.4906051 L71,45.2521732 C67.9132078,45.3225466 65.0300637,43.7281799 63.4658378,41.085813 C61.901612,38.4434462 61.901612,35.1674647 63.4658378,32.5250978 C65.0300637,29.882731 67.9132078,28.2883643 71,28.3587376 L71,28.3587376 Z M50.6440287,44.690734 L33.4479625,44.690734 C32.2727168,44.690734 31.319992,43.7450119 31.319992,42.578456 C31.319992,41.4119 32.2727168,40.4662196 33.4479625,40.4662196 L50.6440287,40.4662196 C51.8160326,40.4710408 52.7649258,41.412918 52.7698574,42.5762561 C52.7745265,43.1377058 52.5522308,43.677591 52.1526714,44.0750231 C51.7531119,44.4724552 51.2096693,44.6942313 50.6440287,44.690734 L50.6440287,44.690734 Z M50.6440287,33.1798743 L33.4479625,33.1798743 C32.2727168,33.1798743 31.319992,32.2341521 31.319992,31.0675962 C31.319992,29.9010402 32.2727168,28.9553598 33.4479625,28.9553598 L50.6440287,28.9553598 C51.8160326,28.960181 52.7649258,29.9020582 52.7698574,31.0653964 C52.7745265,31.626846 52.5522308,32.1667313 52.1526714,32.5641633 C51.7531119,32.9615954 51.2096693,33.1833715 50.6440287,33.1798743 L50.6440287,33.1798743 Z" id="形状"></path>
                            <path d="M10.9662369,43.1421367 C15.2221779,43.1421367 19.3631571,41.4532386 19.3631571,36.8076831 C19.6106418,32.3375555 15.1072722,30.543345 10.9662369,30.4731738 L10.9662369,17.9445533 C10.9662369,16.006368 12.5491505,14.4351584 14.5017734,14.4351584 L59.0498137,14.4351584 L54.546388,2.29247946 C53.8693447,0.476287226 51.8381478,-0.45227234 50.0076153,0.217584189 L2.31296712,17.64627 C1.43104695,17.9688804 0.714839401,18.6267826 0.322593888,19.4746102 C-0.0696516247,20.3224378 -0.105647479,21.2904019 0.222559779,22.1646264 L10.9662369,51.1173152 L10.9662369,43.1640791 L10.9662369,43.1421367 Z" id="路径"></path>
                        </g>
                    </g>
                </g>
                <text id="该商品暂不可用" mask="url(#mask-2)" font-family="PingFangSC-Regular, PingFang SC" font-size="12" font-weight="normal" fill="#747474">
                    <tspan x="77" y="106"></tspan>
                </text>
            </g>
        </g>
    </g>
</svg>