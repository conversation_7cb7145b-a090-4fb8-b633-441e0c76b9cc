<template>
  <div
    class="agent-content"
    :style="{
      paddingLeft: datas.pageMargin + 'px',
      paddingRight: datas.pageMargin + 'px',
    }"
  >
    <div class="title">{{ datas.compName }}</div>
    <!-- 根据 swiperType 渲染不同布局容器 -->
    <div v-if="datas.swiperType == 0" class="agent-list agent-list-single">
      <div
        class="agent-item agent-item-single"
        :style="{
          marginBottom: datas.agentSpacing + 'px',
        }"
        v-for="(item, index) in staticAgentList"
        :key="index"
      >
        <div class="noImg one">
          <img src="@/assets/images/empty.png" alt="" />
        </div>
        <div class="agent-info">
          <div>
            <div class="agent-name">{{ item.name }}</div>
            <div class="agent-desc">{{ item.desc }}</div>
          </div>
          <div class="agent-browse" v-if="datas.visitorsShow">
            <img src="@/assets/images/eyes_0.png" alt="" class="eyes" />
            <span class="people">浏览人数</span>
          </div>
        </div>
      </div>
    </div>
    <div v-else-if="datas.swiperType == 1" class="agent-list agent-list-double">
      <div
        class="agent-item agent-item-double"
        v-for="(item, index) in staticAgentList"
        :key="index"
        :style="{
          marginBottom: datas.agentSpacing + 'px',
          width: 'calc(50% - ' + datas.agentSpacing / 2 + 'px)',
        }"
      >
        <div class="noImg two">
          <img src="@/assets/images/empty.png" alt="" />
          <div class="seeNum" v-if="datas.visitorsShow">
            <img src="@/assets/images/eyes.png" alt="" class="eyes" />
            <span class="people">浏览人数</span>
          </div>
        </div>
        <div class="agent-name">{{ item.name }}</div>
        <div class="agent-desc">{{ item.desc }}</div>
      </div>
    </div>
    <!-- 删除组件插槽 -->
    <slot name="deles" />
  </div>
</template>

<script>
export default {
  name: 'agent',
  props: {
    datas: Object,
  },
  data() {
    return {
      staticAgentList: [
        {
          avatar: 'https://via.placeholder.com/60',
          name: '智慧导游A',
          desc: '这里展示智能体简介，最多展示两行',
        },
        {
          avatar: 'https://via.placeholder.com/60',
          name: '智慧导游B',
          desc: '这里展示智能体简介，最多展示两行',
        },
        {
          avatar: 'https://via.placeholder.com/60',
          name: '智慧导游C',
          desc: '这里展示智能体简介，最多展示两行',
        },
        {
          avatar: 'https://via.placeholder.com/60',
          name: '智慧导游D',
          desc: '这里展示智能体简介，最多展示两行',
        },
      ],
    }
  },
}
</script>

<style scoped lang="less">
.agent-content {
  position: relative;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 16px;

  .title {
    color: #14131f;
    font-size: 19px;
    font-weight: 700;
    margin-bottom: 12px;
  }

  // 通用列表清除浮动
  .agent-list {
    &:after {
      content: '';
      display: table;
      clear: both;
    }
  }

  // 一行一个的布局
  .agent-list-single {
    .agent-item-single {
      display: flex;
      align-items: center;
      padding: 9px;
      border-bottom: 1px dashed #f0f0f0;
      transition: background-color 0.2s;
      box-shadow: 0px 1px 3px 2px rgba(209, 209, 209, 0.5);

      &:hover {
        background-color: #fafafa;
      }

      .noImg.one {
        margin-right: 12px;
        padding-top: 32px;
        height: 107px;
        width: 107px;
        background: #f3f5f6;
        text-align: center;
        box-sizing: border-box;
        img {
          width: 58px;
          height: 46px;
        }
      }

      .agent-info {
        flex: 1;
        text-align: left;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 107px;

        .agent-name {
          font-size: 16px;
          color: #333;
          margin-bottom: 4px;
          font-weight: 700;
        }

        .agent-desc {
          font-size: 14px;
          color: #666;
          line-height: 1.5;
          margin-bottom: 6px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .agent-browse {
          font-size: 12px;
          color: #999;
          display: flex;
          align-items: center;
          img {
            width: 16px;
            height: 16px;
            margin-right: 5px;
          }
        }
      }
    }
  }

  // 一行两个的布局 - 修复对齐问题
  .agent-list-double {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between; /* 保持两端对齐 */

    /* 添加这个伪元素确保最后一行正确对齐 */
    &::after {
      content: '';
      width: calc(50% - 8px); /* 与项目宽度匹配 */
    }

    .agent-item-double {
      /* 宽度由动态样式控制 */
      background: #f9f9f9;
      border: 1px solid #eee;
      border-radius: 6px;
      box-sizing: border-box;
      overflow: hidden;

      /* 确保所有项目高度一致 */
      display: flex;
      flex-direction: column;
      height: auto; /* 高度自适应内容 */

      .noImg.two {
        width: 100%;
        height: 177px;
        padding-top: 62px;
        position: relative;
        background: #f3f5f6;
        text-align: center;
        box-sizing: border-box;
        img {
          width: 58px;
          height: 46px;
        }
        .seeNum {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 34px;
          display: flex;
          align-items: center;
          text-align: left;
          padding-left: 8px;
          color: #fff;
          background: linear-gradient(
            180deg,
            rgba(0, 0, 0, 0) 0%,
            rgba(0, 0, 0, 0.8) 100%
          );
          .eyes {
            width: 20px;
            height: 20px;
            margin-right: 5px;
          }
        }
      }

      .agent-name {
        font-size: 16px;
        color: #333;
        font-weight: 600;
        padding: 12px 8px 0;
        margin: 0;
        /* 确保标题区域高度一致 */
        min-height: 40px; /* 根据实际需要调整 */
      }

      .agent-desc {
        padding: 6px 8px 12px;
        font-size: 14px;
        color: #666;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        margin: 0;
        flex-grow: 1; /* 描述区域填充剩余空间 */
      }
    }
  }
}
</style>
