<template>
  <div class="magiccubestyle">
    <!-- 魔方密度 -->
    <div v-if="datas.rubiksStyle.type === 7">
      <titlecom title="魔方密度" />
      <el-select
        style="width: 100%"
        v-model="datas.rubiksStyle.number"
        @change="changeCubeNum"
        placeholder="请选择"
      >
        <el-option
          v-for="(value, key) in cubeNumList"
          :key="key"
          :value="key"
          :label="value"
        ></el-option>
      </el-select>
      <div class="bor"></div>
    </div>

    <titlecom
      title="魔方布局"
      tips="选定布局区域，在下方添加图片，建议添加比例一致的图片"
    />

    <div style="position: relative">
      <!-- 图片布局 -->
      <div
        class="rubikes-cube"
        :style="{
          width: `${375 * scale}px`,
          height: `${datas?.rubiksStyle?.height * scale}px`,
        }"
      >
        <div
          @click="imgActive = index"
          v-for="(item, index) in datas?.rubiksStyle?.list"
          :key="index"
          class="rubikes-cube-item"
          :class="{ 'rubikes-cube-active': imgActive === index }"
          :style="{
            top: `${item.top * scale}px`,
            left: `${item.left * scale}px`,
            width: `${item.width * scale}px`,
            height: `${item.height * scale}px`,
            zIndex: imgActive === index ? 2 : '',
            // outline: imgActive === index ? '1px solid #1890FF' : 'none',
            backgroundColor: '#fff',
            backgroundImage: `url(${item.src})`,
          }"
        >
          <div
            v-if="!item.src"
            :style="{
              width: '100%',
              height: '100%',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              fontSize: '12px',
              textAlign: 'center',
              backgroundColor:
                imgActive === index ? 'rgba(24, 144, 255, 0.15)' : '#fff',
              color: imgActive === index ? '#1890FF' : '',
            }"
          >
            {{ Math.floor(item.width * 2) }}x{{ Math.floor(item.height * 2) }}
            {{ Math.floor(item.width * 2) > 130 ? '像素或同等比例' : '' }}
          </div>
          <van-icon
            @click="delCubeItem(index)"
            v-if="imgActive === index && datas.rubiksStyle.type === 7"
            class="imgClose"
            name="close"
          />
        </div>
      </div>
      <!-- 自定义魔方 -->
      <div
        v-if="datas.rubiksStyle.type === 7"
        class="grid-container"
        :style="{
          'grid-template-columns': `repeat(${datas.rubiksStyle.number}, 1fr)`,
          width: `${375 * scale}px`,
          height: `${375 * scale}px`,
        }"
      >
        <div
          v-for="cell in datas.rubiksStyle.number * datas.rubiksStyle.number"
          :key="cell"
          class="cell"
          :class="{ selected: isSelected(cell) }"
          @mouseenter="handleMouseEnter(cell)"
          @mousedown="onSelection(cell)"
        >
          <span class="cell-icon">+</span>
        </div>
      </div>
    </div>

    <!-- 表单 -->
    <el-form label-width="80px" label-position="left" :model="datas">
      <section
        class="magiccubestyleList"
        v-for="(item, index) in datas?.rubiksStyle?.list"
        :key="index"
        v-show="imgActive === index"
      >
        <!-- 图片 -->
        <upload v-model="item.src" />
        <!-- 标题和链接 -->
        <div class="imgText">
          <span>链接设置</span>
          <linkcom :element="item" />
        </div>
      </section>
      <div class="bor"></div>
      <titlecom title="选择模版" />
      <!-- 商品样式选择 -->
      <div class="rubiksCubeType">
        <div
          class="rubiksCubeType__item"
          :class="[item.style.type === datas.rubiksStyle.type ? 'active' : '']"
          @click="changeCubeType(item)"
          v-for="(item, index) in rubiksCubeTypes"
          :key="index"
        >
          <svg class="item__svg" aria-hidden="true">
            <use :xlink:href="'#' + item.icon"></use>
          </svg>
          <div style="margin-top: 12px; margin-bottom: 20px">
            {{ item.content }}
          </div>
        </div>
      </div>

      <div class="bor"></div>
      <titlecom title="组件样式" />
      <!-- 页面边距 -->
      <el-form-item label="页面边距">
        <el-slider v-model="datas.pageMargin" :max="20" show-input> </el-slider>
      </el-form-item>

      <!-- 图片间距 -->
      <el-form-item label="图片间距">
        <el-slider v-model="datas.imgMargin" :max="20" show-input> </el-slider>
      </el-form-item>
      <!-- 图片圆角 -->
      <el-form-item label="图片圆角">
        <el-slider v-model="datas.borderRadius" :max="55" show-input>
        </el-slider>
      </el-form-item>
    </el-form>

    <!-- 上传图片 -->
    <uploadimg ref="upload" @uploadInformation="uploadInformation" />
  </div>
</template>

<script>
import uploadimg from '../../uploadImg' //图片上传
import upload from '../../upload' //图片上传
export default {
  name: 'magiccubestyle',
  props: {
    datas: {
      type: Object,
      default: () => ({
        rubiksStyle: {
          height: 375,
          number: 4,
          list: [],
        },
        pageMargin: 0,
        imgMargin: 0,
      }),
    },
  },
  components: { uploadimg, upload },
  data() {
    return {
      cubeNumList: { 4: '4X4', 5: '5X5', 6: '6X6', 7: '7X7' },
      finalSelectedCells: [],
      activeee: 0,
      scale: 0.876,
      rubiksCubeTypes: [
        {
          icon: 'cube_type_0',
          content: '一行二个',
          style: {
            type: 0,
            height: 187.5,
            list: [
              {
                top: 0,
                left: 0,
                width: 187.5,
                height: 187.5,
                src: '',
              },
              {
                top: 0,
                left: 187.5,
                width: 187.5,
                height: 187.5,
                src: '',
              },
            ],
          },
        },
        {
          icon: 'cube_type_1',
          type: 1,
          content: '一行三个',
          style: {
            height: 125,
            type: 1,
            list: [
              {
                top: 0,
                left: 0,
                width: 125,
                height: 125,
                src: '',
              },
              {
                top: 0,
                left: 125,
                width: 125,
                height: 125,
                src: '',
              },
              {
                top: 0,
                left: 250,
                width: 125,
                height: 125,
                src: '',
              },
            ],
          },
        },
        {
          icon: 'cube_type_2',
          content: '一行四个',
          style: {
            height: 93.75,
            type: 2,
            list: [
              {
                top: 0,
                left: 0,
                width: 93.75,
                height: 93.75,
                src: '',
              },
              {
                top: 0,
                left: 93.75,
                width: 93.75,
                height: 93.75,
                src: '',
              },
              {
                top: 0,
                left: 187.5,
                width: 93.75,
                height: 93.75,
                src: '',
              },
              {
                top: 0,
                left: 281.25,
                width: 93.75,
                height: 93.75,
                src: '',
              },
            ],
          },
        },
        {
          icon: 'cube_type_3',
          type: 3,
          content: '二左二右',
          style: {
            height: 375,
            type: 3,
            list: [
              {
                top: 0,
                left: 0,
                width: 187.5,
                height: 187.5,
                src: '',
              },
              {
                top: 0,
                left: 187.5,
                width: 187.5,
                height: 187.5,
                src: '',
              },
              {
                top: 187.5,
                left: 0,
                width: 187.5,
                height: 187.5,
                src: '',
              },
              {
                top: 187.5,
                left: 187.5,
                width: 187.5,
                height: 187.5,
                src: '',
              },
            ],
          },
        },
        {
          icon: 'cube_type_4',
          type: 4,
          content: '一左二右',
          style: {
            height: 375,
            type: 4,
            list: [
              {
                top: 0,
                left: 0,
                width: 187.5,
                height: 375,
                src: '',
              },
              {
                top: 0,
                left: 187.5,
                width: 187.5,
                height: 187.5,
                src: '',
              },
              {
                top: 187.5,
                left: 187.5,
                width: 187.5,
                height: 187.5,
                src: '',
              },
            ],
          },
        },
        {
          icon: 'cube_type_5',
          content: '一上二下',
          style: {
            height: 375,
            type: 5,
            list: [
              {
                top: 0,
                left: 0,
                width: 375,
                height: 187.5,
                src: '',
              },
              {
                top: 187.5,
                left: 0,
                width: 187.5,
                height: 187.5,
                src: '',
              },
              {
                top: 187.5,
                left: 187.5,
                width: 187.5,
                height: 187.5,
                src: '',
              },
            ],
          },
        },
        {
          icon: 'cube_type_6',
          content: '一左三右',
          style: {
            height: 375,
            type: 6,
            list: [
              {
                top: 0,
                left: 0,
                width: 187.5,
                height: 375,
                src: '',
              },
              {
                top: 0,
                left: 187.5,
                width: 187.5,
                height: 187.5,
                src: '',
              },
              {
                top: 187.5,
                left: 187.5,
                width: 93.75,
                height: 187.5,
                src: '',
              },
              {
                top: 187.5,
                left: 281.25,
                width: 93.75,
                height: 187.5,
                src: '',
              },
            ],
          },
        },
        {
          icon: 'cube_type_7',
          content: '自定义',
          style: {
            type: 7,
            height: 375,
            number: '4',
            list: [],
          },
        },
      ],
      imgActive: 0, //默认选中第一个图片
      selectedCells: [],
      allSelectedCells: [],
      optionsType: [
        {
          type: '10',
          name: '内部链接',
        },
        {
          type: '11',
          name: '外部链接',
        },
      ], //跳转类型
      emptyText: '',
    }
  },
  computed: {
    // eslint-disable-next-line vue/return-in-computed-property
    styleText() {
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      this.imgActive = 0
      if (this.datas.rubiksCubeType === 0) return '一行二个'
      if (this.datas.rubiksCubeType === 1) return '一行三个'
      if (this.datas.rubiksCubeType === 2) return '一行四个'
      if (this.datas.rubiksCubeType === 3) return '二左二右'
      if (this.datas.rubiksCubeType === 4) return '一左二右'
      if (this.datas.rubiksCubeType === 5) return '一上二下'
      if (this.datas.rubiksCubeType === 6) return '一左三右'
      if (this.datas.rubiksCubeType === 7) return '自定义'
    },
  },
  created() {
    console.log('createdcreatedcreatedcreated', this.datas)

    // this.changeCubeType(this.rubiksCubeTypes[0])
  },
  methods: {
    // 删除魔方块
    delCubeItem(index) {
      const selectdedCells = this.datas.rubiksStyle.list[index].selectdedCells
      this.datas.rubiksStyle.list.splice(index, 1)
      this.finalSelectedCells = []
      this.allSelectedCells = this.allSelectedCells.filter(
        (cell) => !selectdedCells.includes(cell)
      )
      console.log('del', this.allSelectedCells)
    },
    // 选择魔方样式
    changeCubeType(item) {
      this.datas.rubiksStyle = item.style
    },
    // 改变魔方密度
    changeCubeNum(e) {
      this.datas.rubiksStyle.list = []
      this.finalSelectedCells = []
      this.allSelectedCells = []
    },
    onSelection(cell) {
      console.log('onSelection', cell)
      if (!this.isSelecting) {
        // 开始选择
        this.isSelecting = true
        this.selectedCells = [cell]
        this.calculateMinimumRectangle()
      } else {
        // 选择结束
        this.isSelecting = false
        this.calculateMinimumRectangle(true)
      }
    },
    handleMouseEnter(cell) {
      if (this.isSelecting) {
        this.selectedCells[1] = cell
        this.calculateMinimumRectangle()
      }
    },
    calculateMinimumRectangle(isEnd) {
      // 几乘几
      const num = this.datas.rubiksStyle.number
      // 选中的块在第几行
      const rows = this.selectedCells.map((cell) => Math.ceil(cell / num))
      // 选中的块在第几列
      const cols = this.selectedCells.map((cell) => cell % num || num)
      const minRow = Math.min(...rows)
      const maxRow = Math.max(...rows)
      const minCol = Math.min(...cols)
      const maxCol = Math.max(...cols)

      const cells = []
      for (let row = minRow; row <= maxRow; row++) {
        for (let col = minCol; col <= maxCol; col++) {
          const index = (row - 1) * num + col
          cells.push(index)
        }
      }
      // 判断圈中的块是否已经被选中
      const isOverlap = cells.some((cell) =>
        this.allSelectedCells.includes(cell)
      )
      console.log(isOverlap)
      if (isOverlap) return

      this.finalSelectedCells = cells

      if (isEnd) {
        // 一个块的宽度
        const cellWidth = 375 / num

        // 最小的横坐标
        const minLeft = (minCol - 1) * cellWidth
        // 最小的纵坐标
        const minTop = (minRow - 1) * cellWidth
        // 最大的横坐标
        const maxLeft = maxCol * cellWidth
        // 最大的纵坐标
        const maxTop = maxRow * cellWidth

        this.datas.rubiksStyle.list.push({
          top: minTop,
          left: minLeft,
          width: maxLeft - minLeft,
          height: maxTop - minTop,
          src: '',
          selectdedCells: cells,
        })

        console.log(this.datas.rubiksStyle.list)

        const list = this.allSelectedCells.concat(this.finalSelectedCells)
        this.allSelectedCells = [...new Set(list)]
      }
    },
    isSelected(cell) {
      return this.finalSelectedCells.includes(cell)
    },
    /* 替换 */
    uploadInformation(res) {
      this.datas.imageList[this.imgActive].src = res
    },
  },
}
</script>

<style scoped lang="less">
.magiccubestyle {
  .rubikes-cube {
    // border: 1px solid rgba(0, 0, 0, 0.15);
    position: relative;
    .rubikes-cube-item {
      font-size: 14px;
      position: absolute;
      outline: 1px solid rgba(0, 0, 0, 0.15);
      color: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      background-size: cover;
      z-index: 1;
      &:hover {
        .imgClose {
          display: block;
        }
      }
      .imgClose {
        display: none;
        position: absolute;
        top: -10px;
        right: -10px;
        font-size: 18px;
        z-index: 4;
      }
    }
    .rubikes-cube-active {
      &::after {
        content: '';
        position: absolute;
        inset: 0;
        border: 1px solid #1890ff;
      }
    }
  }

  /* 布局 */
  .buju {
    &.buju0 {
      display: flex;
      justify-content: space-around;
      flex-wrap: wrap;
    }
    &.buju4 {
      display: inline-flex;
      flex-direction: row;
      justify-content: space-around;
    }
    .active {
      background: #e0edff;
      border: 1px solid #1890ff;
      color: #1890ff;
      z-index: 2;
    }
    .rubiksCubeType {
      background-color: #fff;
      border: 1px solid #ebedf0;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      color: #7d7e80;
      cursor: pointer;
      &.active {
        background: #e0edff;
        border: 1px solid #1890ff;
        color: #1890ff;
        z-index: 2;
      }
      &.rubiksCubeType0 {
        width: 163px;
        margin: 10px 0;
        // height: 200px;
        img {
          width: 100%;
          // height: 200px;
        }
      }
      &.rubiksCubeType1 {
        width: 109px;
        margin: 10px 0;
        height: 150px;
        img {
          width: 100%;
          height: 150px;
        }
      }
      &.rubiksCubeType2 {
        width: 82px;
        margin: 10px 0;
        height: 150px;
        img {
          width: 100%;
          height: 150px;
        }
      }
      &.rubiksCubeType3 {
        width: 163px;
        margin: 10px 0;
        height: 163px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      &.rubiksCubeType4 {
        width: 163px;
        height: 163px;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  /* 商品列表 */
  .magiccubestyleList {
    padding: 10px;
    margin: 15px 0;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 0 4px 0 rgba(10, 42, 97, 0.2);
    display: flex;
    gap: 10px;
    .imgText {
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      justify-content: space-between;
    }
  }

  /* 列表样式 */
  .rubiksCubeType {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 0px 30px;
    .rubiksCubeType__item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      .item__svg {
        width: 60px;
        height: 60px;
        color: #777;
      }
      &:hover,
      &.active {
        .item__svg {
          color: #1890ff;
        }
        color: #1890ff;
      }
    }
  }
}

.grid-container {
  position: absolute;
  top: 0;
  display: grid;
  outline: 1px solid #ccc;
  width: 150px;
  user-select: none;
}

.cell {
  padding-top: calc(100% - 2px); /* 保持格子的正方形 */
  position: relative;
  border: 1px solid #ccc;
  box-sizing: border-box;
  .cell-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

// .cell.selected::after {
.cell.selected {
  // content: '';
  // position: absolute;
  // top: 0;
  // left: 0;
  // right: 0;
  // bottom: 0;
  background-color: rgba(24, 144, 255, 0.15) !important;
  border: none !important;
  .cell-icon {
    display: none;
  }
}

.cube-box {
  position: relative;
  height: 188px;
  &::after {
    content: '';
    position: absolute;
    inset: 0;
    border: 1px solid rgba(0, 0, 0, 0.15);
  }
  .cube-item {
    position: relative;
    &::after {
      content: '';
      position: absolute;
      // inset: 0;
      border: 1px solid rgba(0, 0, 0, 0.15);
    }
  }
}
</style>
