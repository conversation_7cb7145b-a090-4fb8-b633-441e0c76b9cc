<template>
  <div class="sliderassembly">
    <h2>组件库</h2>
    <div>
      <el-collapse v-model="activeNames">
        <el-collapse-item
          :title="items.title"
          :name="index + 1"
          v-for="(items, index) in datas"
          :key="index"
        >
          <div
            class="componList"
            draggable="true"
            @dragstart="drag($event)"
            @dragend="dragends($event)"
            :data-name="item.name"
            v-for="(item, ind) in items.comList"
            :key="ind"
          >
            <!-- <i class="iconfont" :class="item.icon" v-if="item.icon" />
            <van-icon :name="item.vanIcon" v-else /> -->
            <svg class="svgIcon" aria-hidden="true">
              <use :xlink:href="'#' + item.name"></use>
            </svg>
            <p>{{ item.text }}</p>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script>
import { reactive } from 'vue'
export default {
  name: 'sliderassembly',
  props: {
    pointer: Object,
  },
  setup(props) {
    // 侧边栏组件显示
    const activeNames = reactive([1, 2, 3, 4])

    // 组件信息配置
    const datas = reactive([
      {
        title: '基础组件',
        comList: [
          {
            text: '图片广告',
            type: '1-1',
            icon: 'icon-tupianguanggao',
            name: 'pictureads',
          },
          {
            text: '图文导航',
            type: '1-2',
            icon: 'icon-icon_tupiandaohang',
            name: 'graphicnavigation',
          },
          {
            text: '商品分组',
            type: '1-3',
            icon: 'icon-goods',
            name: 'productgroup',
          },
          {
            text: '文章管理',
            type: '1-4',
            icon: 'icon-dianpubijikapian',
            name: 'articlemanage',
          },
          {
            text: '辅助分割',
            type: '1-5',
            icon: 'icon-segmentation',
            name: 'segmentation',
          },
          {
            text: '图片魔方',
            type: '1-6',
            icon: 'icon-magiccube',
            name: 'magiccube',
          },
          {
            text: '标题文本',
            type: '1-7',
            icon: 'icon-dianpubijikapian',
            name: 'titletext',
          },
          {
            text: '全局搜索',
            type: '1-8',
            icon: 'icon-dianpubijikapian',
            name: 'globalsearch',
          },
          {
            text: '顶部图片',
            type: '1-9',
            icon: 'icon-topimg',
            name: 'topimg',
          },
        ],
      },
      {
        title: '营销组件',
        comList: [
          {
            text: '商品工坊',
            type: '2-1',
            icon: 'icon-productWorkshop',
            name: 'productWorkshop',
          },
          {
            text: '目的地精选',
            type: '2-2',
            icon: 'icon-productWorkshop',
            name: 'destinationSelection',
          },
        ],
      },
      {
        title: '游客体验',
        comList: [
          {
            text: '智能体',
            type: '3-1',
            icon: 'icon-agent',
            name: 'agent',
          },
          {
            text: '攻略定制',
            type: '3-2',
            icon: 'icon-strategyCustomization',
            name: 'strategyCustomization',
          },
        ],
      },
      /*
      {
        title: '基础组件',
        comList: [
          {
            text: '商品搜索',
            type: '1-1',
            icon: 'icon-shangpinsousuo',
            name: 'commoditysearch',
          },
          {
            text: '标题文本',
            type: '1-3',
            icon: 'icon-Component-biaotiwenzi',
            name: 'captiontext',
          },
          {
            text: '图片广告',
            type: '1-3',
            icon: 'icon-tupianguanggao',
            name: 'pictureads',
          },
          {
            text: '图文导航',
            type: '1-4',
            icon: 'icon-icon_tupiandaohang',
            name: 'graphicnavigation',
          },
          {
            text: '底部导航',
            type: '1-5',
            icon: 'icon-daohang',
            name: 'tabBar',
          },
          {
            text: '魔方',
            type: '1-6',
            icon: 'icon-mofang',
            name: 'magiccube',
          },
          {
            text: '公告',
            type: '1-7',
            icon: 'icon-gonggao',
            name: 'notice',
          },
          {
            text: '视频',
            type: '1-8',
            icon: 'icon-shipin',
            name: 'videoss',
          },
          {
            text: '富文本',
            type: '1-10',
            icon: 'icon-fuwenben',
            name: 'richtext',
          },
          {
            text: '辅助分割',
            type: '1-11',
            icon: 'icon-Component-fuzhufenge',
            name: 'auxiliarysegmentation',
          },
          {
            text: '店铺信息',
            type: '1-12',
            icon: 'icon-dianpuxinxi',
            name: 'storeinformation',
          },
          {
            text: '单元格',
            type: '1-13',
            icon: 'icon-jinrudianpu',
            name: 'entertheshop',
          },
          {
            text: '社群涨粉',
            type: '1-14',
            icon: 'icon-kuaisuzhangfen',
            name: 'communitypowder',
          },
          {
            text: '关注公众号',
            type: '1-15',
            icon: 'icon-gongzhonghao',
            name: 'follow',
          },
          {
            text: '悬浮',
            type: '1-16',
            icon: 'icon-wangye',
            name: 'suspension',
          },
          {
            text: '自定义模块',
            type: 'demo',
            icon: 'icon-zidingyimokuai',
            name: 'custommodule',
          },
        ],
      },
      {
        title: '业务组件',
        comList: [
          {
            text: '商品',
            type: '2-1',
            icon: 'icon-goods',
            name: 'listswitching',
          },
          {
            text: '文章模块',
            type: '2-2',
            icon: 'icon-dianpubijikapian',
            name: 'storenotecard',
          },
          {
            text: '表单模块',
            type: '2-3',
            vanIcon: 'orders-o',
            name: 'investigate',
          },
        ],
      },
      */
    ])

    /**
     * 当用户开始拖动元素或选择文本时触发此事件
     *
     * @param {Object} event event 对象
     */
    const drag = (event) => {
      /* 开启穿透 */
      props.pointer.show = true
      /* 传递参数 */
      event.dataTransfer.setData('componentName', event.target.dataset.name)
    }

    /**
     * 当拖动操作结束时（释放鼠标按钮或按下退出键），会触发此事件
     *
     * @param {Object} event event 对象
     */
    const dragends = () => {
      /* 关闭穿透 */
      props.pointer.show = false
    }

    return {
      activeNames,
      datas,
      drag,
      dragends,
    }
  },
}
</script>

<style scoped lang="less">
/* 组件 */
.sliderassembly {
  width: 280px;
  height: 100%;
  border-right: 1px solid #ebedf0;
  box-sizing: content-box;
  background: #fff;
  display: flex;
  flex-direction: column;
  h2 {
    width: 100%;
    height: 48px;
    padding: 0 20px;
    font-size: 18px;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.85);
    line-height: 48px;
    border-bottom: 1px solid #ebeef5;
  }
  > div {
    padding: 0 20px;
    height: 0;
    flex: 1;
    overflow: auto;
  }
  :deep(.el-collapse) {
    border: none;
    .el-collapse-item:last-child {
      .el-collapse-item__wrap,
      .el-collapse-item__header {
        border-bottom: none;
      }
    }
    .el-collapse-item__header {
      font-size: 16px;
      font-weight: bold;
    }
  }
  /* 组件列表 */
  .componList {
    width: 80px;
    height: 80px;
    display: inline-flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: all-scroll;
    transition: all 0.3s;
    &:hover {
      background: #1890ff;
      border-radius: 2px;
      font-weight: bold;
      .svgIcon,
      p {
        color: #fff;
      }
    }
    .svgIcon {
      width: 40px;
      height: 40px;
      color: #6a6a6a;
    }
    p {
      font-size: 14px;
      color: #6a6a6a;
      margin-top: 4px;
    }
    // /* 图标 */
    // i {
    //   font-size: 32px;
    //   width: 32px;
    //   height: 32px;
    //   line-height: 32px;
    //   color: #b0a8a8;
    //   margin-top: 4px;
    // }
    // /* 数量 */
    // span {
    //   color: #7d7e80;
    //   margin-top: 4px;
    //   font-size: 10px;
    // }
  }
}
</style>
